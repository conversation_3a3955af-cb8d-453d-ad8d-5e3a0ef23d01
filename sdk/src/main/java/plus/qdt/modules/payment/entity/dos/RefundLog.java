package plus.qdt.modules.payment.entity.dos;

import plus.qdt.common.enums.ResultCode;
import plus.qdt.common.exception.ServiceException;
import plus.qdt.common.utils.StringUtils;
import plus.qdt.mybatis.model.BaseEntity;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 退款日志
 *
 * <AUTHOR>
 * @since 2021/1/28 09:21
 */
@Data
@TableName("li_refund_log")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(title = "退款日志")
@EqualsAndHashCode(callSuper = true)
public class RefundLog extends BaseEntity {

    private static final long serialVersionUID = 3206432601756442476L;

    @Schema(title = "用户ID")
    private String userId;

    @Schema(title = "订单编号")
    private String orderSn;

    @Schema(title = "退单编号")
    private String afterSaleNo;

    @Schema(title = "退款金额")
    private Double price;

    @Schema(title = "该笔交易实际支付金额")
    private Double payPrice;

    @Schema(title = "是否已退款")
    private Boolean isRefund;

    @Schema(title = "退款方式")
    private String paymentMethod;

    @Schema(title = "第三方支付发起交易号")
    private String outTradeNo;

    @Schema(title = "第三方平台付款流水号")
    private String transactionId;


    @Schema(title = "第三方退款请求流水")
    private String outRefundNo;

    @Schema(title = "第三方平台退款流水号")
    private String refundTransactionId;

    @Schema(title = "退款理由")
    private String refundReason;

    @Schema(title = "退款失败原因")
    private String errorMessage;


    @Schema(title = "商户ID")
    private String mchId;

    @CreatedDate
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    @Schema(title = "创建时间", hidden = true)
    private Date createTime;

    public Double getPrice() {
        if (price == null || price <= 0 || price > payPrice) {
            throw new ServiceException(ResultCode.AFTER_SALE_PRICE_ERROR);
        }
        return price;
    }


    public String generateRefundDescription() {
        return String.format("订单[%s]退款[%s]元", orderSn, StringUtils.formatPrice(price));
    }

}