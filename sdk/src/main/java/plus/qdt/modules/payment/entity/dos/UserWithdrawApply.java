package plus.qdt.modules.payment.entity.dos;

import plus.qdt.modules.payment.entity.enums.WithdrawStatusEnum;
import plus.qdt.mybatis.model.BaseStandardEntity;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 用户提现申请
 *
 * <AUTHOR>
 * @since 2020-02-25 14:10:16
 */
@Data
@TableName("li_user_withdraw_apply")
@Schema(title = "用户提现申请")
public class UserWithdrawApply extends BaseStandardEntity {

    private static final long serialVersionUID = 1L;

    @Schema(title = "申请提现金额")
    private Double applyMoney;

    /**
     * @see WithdrawStatusEnum
     */
    @Schema(title = "提现状态")
    private String applyStatus;

    @Schema(title = "用户id")
    private String userId;

    @Schema(title = "用户名")
    private String username;

    @Schema(title = "审核备注")
    private String inspectRemark;

    @Schema(title = "审核时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private Date inspectTime;

    @Schema(title = "sn")
    private String sn;

    @Schema(title = "真实姓名")
    private String realName;

    @Schema(title = "第三方平台账号")
    private String connectNumber;

}
