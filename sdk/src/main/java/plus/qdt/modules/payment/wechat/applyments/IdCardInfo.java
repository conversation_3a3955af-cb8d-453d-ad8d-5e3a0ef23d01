package plus.qdt.modules.payment.wechat.applyments;

import plus.qdt.modules.payment.entity.SpecEncrypt;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 经营者/法人身份证信息
 *
 * <AUTHOR>
 */
@Data
public class IdCardInfo {

    /**
     * 1、证件类型为“身份证”时，上传身份证人像面照片。
     * 2、可上传1张图片，请填写通过图片上传API预先上传图片生成好的MediaID。
     * 3、请上传彩色照片or彩色扫描件or复印件（需加盖公章鲜章），可添加“微信支付”相关水印（如微信支付认证）。
     */
    @Schema(title = "身份证人像面照片")
    String id_card_copy;

    /**
     * 1、证件类型为“身份证”时，上传身份证国徽面照片。
     * 2、可上传1张图片，请填写通过图片上传API预先上传图片生成好的MediaID 。
     * 3、请上传彩色照片or彩色扫描件or复印件（需加盖公章鲜章），可添加“微信支付”相关水印（如微信支付认证）。
     */
    @Schema(title = "身份证国徽面照片")
    String id_card_national;

    /**
     * 1、请填写经营者/法定代表人对应身份证的姓名，2~30个中文字符、英文字符、符号。
     * 2、该字段需进行加密处理，加密方法详见敏感信息加密说明。(提醒：必须在HTTP头中上送Wechatpay-Serial)
     */
    @Schema(title = "身份证姓名")
    @SpecEncrypt
    String id_card_name;

    /**
     * 1、请填写经营者/法定代表人对应身份证的号码。
     * 2、15位数字或17位数字+1位数字|X ，该字段需进行加密处理，加密方法详见敏感信息加密说明。(提醒：必须在HTTP头中上送Wechatpay-Serial)
     */
    @Schema(title = "身份证号码")
    @SpecEncrypt
    String id_card_number;

    /**
     * 1、主体类型为企业时，需要填写。其他主体类型，无需上传。
     * 2、请按照身份证住址填写，如广东省深圳市南山区xx路xx号xx室
     * 3、该字段需进行加密处理，加密方法详见敏感信息加密说明。(提醒：必须在HTTP头中上送Wechatpay-Serial)
     */
    @Schema(title = "身份证居住地址")
    @SpecEncrypt
    String id_card_address;

    /**
     * 1、请按照示例值填写。
     * 2、结束时间大于开始时间。
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(title = "身份证开始时间")
    String id_card_valid_time_begin;

    /**
     * 1、请按照示例值填写，若证件有效期为长期，请填写：长期。
     * 2、结束时间大于开始时间。
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(title = "身份证结束时间")
    String id_card_valid_time;


}
