package plus.qdt.modules.payment.entity.dos;

import plus.qdt.mybatis.model.BaseEntity;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 合单支付日志
 *
 * <AUTHOR>
 * @since 2021/1/28 09:21
 */
@Data
@TableName("li_combine_payment_log")
@Builder
@Schema(title = "合单支付日志")
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CombinePaymentLog extends BaseEntity {


    private static final long serialVersionUID = 5349949725444117492L;

    @Schema(title = "订单编号")
    private String orderSn;

    /**
     * @see plus.qdt.modules.payment.entity.enums.PaymentClientEnum
     */
    @Schema(title = "订单支付方式")
    private String paymentClient;

    /**
     * @see plus.qdt.modules.payment.entity.enums.PaymentMethodEnum
     */
    @Schema(title = "支付方式")
    private String paymentMethod;

    @Schema(title = "已校验回查")
    private Boolean isCheck = false;

    @Schema(title = "合单商户APPID")
    private String combineAppid;

    @Schema(title = "合单商户号")
    private String combineMchid;

    @Schema(title = "合单支付总金额")
    private Double price;

    @Schema(title = "合单支付对第三方请求单号")
    private String combineOutTradeNo;

    @Schema(title = "支付请求参数")
    private String paymentRequestSource;

    @Schema(title = "支付响应")
    private String paymentResponseSource;

    @Schema(title = "付款人ID")
    private String payerId;

    @Schema(title = "付款人OPENID")
    private String openid;


    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "支付过期时间", hidden = true)
    private Date timeoutExpress;

    @CreatedDate
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    @Schema(title = "创建时间", hidden = true)
    private Date createTime;

}