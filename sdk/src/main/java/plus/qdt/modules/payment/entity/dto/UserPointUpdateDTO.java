package plus.qdt.modules.payment.entity.dto;

import plus.qdt.common.security.enums.SceneEnums;
import plus.qdt.modules.payment.entity.enums.UserPointServiceEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 会员余额变动模型
 *
 * <AUTHOR>
 * @version v1.0
 * 2021-12-01 09:35
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserPointUpdateDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -2535936006829829167L;

    @Schema(title = "用户id")
    private String userId;

    @Schema(title = "变动积分")
    private Long points;

    @Schema(title = "企通宝")
    private Long qtbCount;

    @Schema(title = "积分业务枚举")
    private UserPointServiceEnum userPointServiceEnum;

    @Schema(title = "积分业务描述")
    private String description;

    @Schema(title = "场景")
    private SceneEnums scene = SceneEnums.MEMBER;

    @Schema(title = "关联编号")
    private String snReference;

}
