package plus.qdt.modules.payment.entity.dto;

import cn.hutool.core.util.StrUtil;
import plus.qdt.common.vo.PageVO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

@Data
public class WechatBillSearchParams extends PageVO {

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "起始日期")
    private String startDate;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "结束日期")
    private String endDate;


    /**
     * @see plus.qdt.modules.payment.entity.enums.BillTypeEnum
     */
    @Schema(title = "账单类型")
    private String billType;

    /**
     * 不填则默认是BASIC
     * 枚举值：
     * BASIC：基本账户
     * OPERATION：运营账户
     * FEES：手续费账户
     */
    @Schema(title = "资金账户类型")
    private String accountType;


    public <T> QueryWrapper<T> queryWrapper() {
        QueryWrapper<T> wrapper = new QueryWrapper<>();

        //创建时间
        if (StrUtil.isNotBlank(startDate) && StrUtil.isNotBlank(endDate)) {
            wrapper.between("bill_date", startDate, endDate);
        } else if (StrUtil.isNotBlank(startDate)) {
            wrapper.ge("bill_date", startDate);
        } else if (StrUtil.isNotBlank(endDate)) {
            wrapper.le("bill_date", endDate);
        }
        wrapper.eq(StrUtil.isNotBlank(billType), "bill_type", billType);
        wrapper.eq(StrUtil.isNotBlank(accountType), "account_type", accountType);
        wrapper.orderByDesc("create_time");
        return wrapper;
    }
}
