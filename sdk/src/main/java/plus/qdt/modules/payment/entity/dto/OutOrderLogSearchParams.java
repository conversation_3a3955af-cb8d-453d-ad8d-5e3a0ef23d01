package plus.qdt.modules.payment.entity.dto;

import cn.hutool.core.text.CharSequenceUtil;
import plus.qdt.common.vo.PageVO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 搜索参数
 *
 * <AUTHOR>
 * @since 2021/3/17 6:08 下午
 */
@Data
public class OutOrderLogSearchParams extends PageVO {

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "起始日期")
    private String startDate;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "结束日期")
    private String endDate;

    @Schema(title = "二级商户号")
    private String subMchid;

    @Schema(title = "订单单号")
    private String orderSn;

    /**
     * @see plus.qdt.modules.order.order.entity.enums.ProfitSharingStatusEnum
     */
    @Schema(title = "状态：PROCESSING：处理中,FINISHED：分账完成")
    private String status;

    public <T> QueryWrapper<T> queryWrapper() {
        QueryWrapper<T> wrapper = new QueryWrapper<>();

        //创建时间
        if (CharSequenceUtil.isNotBlank(startDate) && CharSequenceUtil.isNotBlank(endDate)) {
            wrapper.between("create_time", startDate, endDate);
        } else if (CharSequenceUtil.isNotBlank(startDate)) {
            wrapper.ge("create_time", startDate);
        } else if (CharSequenceUtil.isNotBlank(endDate)) {
            wrapper.le("create_time", endDate);
        }
        //账单号
        wrapper.eq(CharSequenceUtil.isNotBlank(subMchid), "sub_mchid", subMchid);
        wrapper.eq(CharSequenceUtil.isNotBlank(status), "status", status);
        wrapper.eq(CharSequenceUtil.isNotBlank(orderSn), "order_sn", orderSn);
        return wrapper;
    }

}
