package plus.qdt.modules.payment.wechat.model.profitsharing;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 微信支付-分账
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ProfitSharingResponse {

    //二级商户号
    private String sub_mchid;
    //微信订单号
    private String transaction_id;
    //商户分账单号
    private String out_order_no;
    //微信分帐单号
    private String order_id;
    //分账单状态
    private String status;
    //分账接收方列表
    private List<ReceiverResponse> receivers;

}
