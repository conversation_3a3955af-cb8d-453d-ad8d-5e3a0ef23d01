package plus.qdt.modules.payment.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * 第三方回调参数
 *
 * <AUTHOR>
 * @since 2020/12/19 11:46
 */
@Data
@ToString
@NoArgsConstructor
public class ThirdPayCallbackParam implements Serializable {


    @NotNull
    @Schema(title = "支付单号")
    private String sn;

    private Boolean isCombine;

    public ThirdPayCallbackParam(PayParam payParam) {
        this.sn = payParam.getCombineSn();
        this.isCombine = payParam.getIsCombine();
    }
}
