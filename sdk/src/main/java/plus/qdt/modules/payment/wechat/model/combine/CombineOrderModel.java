package plus.qdt.modules.payment.wechat.model.combine;

import plus.qdt.modules.payment.wechat.model.Payer;
import plus.qdt.modules.payment.wechat.model.SceneInfo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 合单支付
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CombineOrderModel {

    /**
     * 合单商户appid
     */
    String combine_appid;

    /**
     * 合单商户号
     */
    String combine_mchid;

    /**
     * 合单商户订单号
     */
    String combine_out_trade_no;

    /**
     * 场景信息
     */
    SceneInfo scene_info;

    /**
     * 子单信息
     */
    List<SubOrder> sub_orders;

    /**
     * 支付者
     */
    Payer combine_payer_info;
    /**
     * 交易起始时间
     */
    String time_start;
    /**
     * 交易结束时间
     */
    String time_expire;

    /**
     * 通知地址
     */
    String notify_url;
}
