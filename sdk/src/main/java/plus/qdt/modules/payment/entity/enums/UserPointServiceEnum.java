package plus.qdt.modules.payment.entity.enums;

/**
 * 用户积分变动枚举
 *
 * <AUTHOR> @ gmail.com)
 * @version v4.0
 * @Description:
 * @since 2022/10/20 17:04
 */
public enum UserPointServiceEnum {

    /**
     * 积分业务枚举
     */
    SIGN(true, "签到赠送积分"),
    PAYMENT_GIFT(true, "支付订单赠送积分"),
    ORDER_GIFT(true, "下单赠送积分"),
    FULL_DISCOUNT_GIFT(true, "满优惠赠送积分"),

    CONSUME(false, "消费积分"),
    ORDER_CANCEL(true, "积分订单取消，返还积分"),
    RETURN(false, "业务回退积分");

    /**
     * 增加积分业务
     */
    private Boolean isAdd;
    /**
     * 业务描述
     */
    private String description;

    UserPointServiceEnum(Boolean isAdd, String description) {
        this.isAdd = isAdd;
        this.description = description;
    }

    public Boolean getAdd() {
        return isAdd;
    }

    public String getDescription() {
        return description;
    }
}
