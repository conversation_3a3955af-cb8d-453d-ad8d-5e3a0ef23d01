package plus.qdt.modules.search.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/6/20
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EsGoodsIndexUpdateDTO {

    /**
     * 查询字段
     */
    private Map<String, Object> queryFields;

    /**
     * 更新字段
     */
    private Map<String, Object> updateFields;

}
