package plus.qdt.modules.search.fallback;

import plus.qdt.common.exception.ServiceException;
import plus.qdt.modules.search.client.CustomWordsClient;
import plus.qdt.modules.search.entity.dos.CustomWords;
import plus.qdt.modules.search.entity.params.CustomWordsParams;
import plus.qdt.modules.search.entity.vo.CustomWordsVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: ftyy
 * @date: 2022-01-17 11:21
 * @description: 自定义分词Fallback
 */
public class CustomWordsFallback implements CustomWordsClient {
    @Override
    public String deploy() {
        throw new ServiceException();
    }

    @Override
    public boolean addCustomWords(CustomWordsVO customWordsVO) {
        throw new ServiceException();
    }

    @Override
    public boolean deleteBathByName(List<String> names) {
        throw new ServiceException();
    }

    @Override
    public void addCustomWordsList(List<CustomWords> customWordsList) {
        throw new ServiceException();
    }

    @Override
    public boolean updateCustomWords(CustomWordsVO customWordsVO) {
        throw new ServiceException();
    }

    @Override
    public boolean deleteCustomWords(String id) {
        throw new ServiceException();
    }

    @Override
    public Page<CustomWords> getCustomWordsByPage(CustomWordsParams customWordsParams) {
        throw new ServiceException();
    }
}
