package plus.qdt.modules.search.entity.params;

import plus.qdt.common.vo.PageVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * @author: ftyy
 * @date: 2022-01-21 18:42
 * @description: 自定义分词params
 */
@Data
@Builder
public class CustomWordsParams {

    @Schema(title = "分词")
    private  String words;

    @Schema(title = "分页")
    private PageVO pageVo;
}
