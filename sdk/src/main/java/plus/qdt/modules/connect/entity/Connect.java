package plus.qdt.modules.connect.entity;

import plus.qdt.mybatis.model.BaseSceneEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@TableName("li_connect")
@Schema(title = "联合登陆")
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class Connect extends BaseSceneEntity {

    private static final long serialVersionUID = 1L;


    @Schema(title = "用户id")
    private String userId;

    @Schema(title = "联合登录id")
    private String unionId;

    /**
     * @see plus.qdt.modules.connect.entity.enums.ConnectEnum
     */
    @Schema(title = "联合登录类型")
    private String unionType;


    public Connect(String userId, String unionId, String unionType) {
        this.userId = userId;
        this.unionId = unionId;
        this.unionType = unionType;
    }
}