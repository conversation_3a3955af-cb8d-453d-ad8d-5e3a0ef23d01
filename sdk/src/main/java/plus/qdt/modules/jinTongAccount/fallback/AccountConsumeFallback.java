package plus.qdt.modules.jinTongAccount.fallback;

import plus.qdt.common.exception.ServiceException;
import plus.qdt.modules.jinTongAccount.client.AccountConsumeClient;
import plus.qdt.modules.jinTongAccount.entity.AccountConsume;
import plus.qdt.modules.jinTongAccount.entity.vo.HostingAccountVo;

import java.math.BigDecimal;

public class AccountConsumeFallback implements AccountConsumeClient {

    @Override
    public long jinTongAccountConsume(AccountConsume accountConsume) {
        throw new ServiceException();
    }

    @Override
    public HostingAccountVo getPayMethod(String tradeSn) {
        throw new ServiceException();
    }

    @Override
    public void releaseMoney(String tradeSn) {
        throw new ServiceException();
    }

    @Override
    public BigDecimal refundMoney(String orderSn, String refundReason, String paymentMethod, boolean b) {
        throw new ServiceException();
    }
}
