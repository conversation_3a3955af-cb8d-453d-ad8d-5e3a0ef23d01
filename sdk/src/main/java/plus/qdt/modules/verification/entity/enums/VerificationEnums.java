package plus.qdt.modules.verification.entity.enums;

/**
 * VerificationEnums
 *
 * <AUTHOR>
 * @version v1.0
 * 2020-12-22 19:11
 */
public enum VerificationEnums {

    /** 登录 */
    LOGIN,
    /** 注册 */
    REGISTER,
    /** 绑定手机号 */
    BIND_MOBILE,
    /** 店铺入驻 */
    STORE_REGISTER,
    /** 找回用户 */
    FIND_USER,
    /** 修改密码 */
    UPDATE_PASSWORD,
    /** 支付钱包密码 */
    WALLET_PASSWORD,
    /** 修改支付钱包密码 */
    WALLET_PASSWORD_UPDATE,
    /** 修改手机号 */
    UPDATE_PHONE,
    /** 绑定新手机号 */
    BIND_NEW_PHONE,
    /** 店铺会员年费到期通知 */
    STORE_VIP_NOTICE,
    /** 会员年费到期通知 */
    VIP_NOTICE,

}


