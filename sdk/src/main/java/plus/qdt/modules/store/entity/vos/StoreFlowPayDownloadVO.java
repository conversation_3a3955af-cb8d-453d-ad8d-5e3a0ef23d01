package plus.qdt.modules.store.entity.vos;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 店铺流水下载
 * <AUTHOR>
 * @date: 2021/8/13 4:14 下午
 *
 */
@Data
public class StoreFlowPayDownloadVO {

    @CreatedDate
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    @Schema(title = "创建时间", hidden = true)
    private Date createTime;

    @Schema(title = "订单sn")
    private String orderSn;

    @Schema(title = "店铺名称 ")
    private String storeName;

    @Schema(title = "商品名称")
    private String goodsName;

    @Schema(title = "销售量")
    private Integer num;

    @Schema(title = "流水金额")
    private Double finalPrice;

    @Schema(title = "平台收取交易佣金")
    private Double commissionPrice;

    @Schema(title = "平台优惠券 使用金额")
    private Double siteCouponPrice;

    @Schema(title = "单品分销返现支出")
    private Double distributionRebate;

    @Schema(title = "积分活动商品结算价格")
    private Double pointSettlementPrice;

    @Schema(title = "砍价活动商品结算价格")
    private Double kanjiaSettlementPrice;

    @Schema(title = "最终结算金额")
    private Double billPrice;
}
