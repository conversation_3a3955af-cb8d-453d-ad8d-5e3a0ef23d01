package plus.qdt.modules.store.client;

import plus.qdt.modules.constant.ServiceConstant;
import plus.qdt.modules.store.fallback.StoreStatisticsFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * <AUTHOR>
 * @since 2022/6/24
 **/
@FeignClient(name = ServiceConstant.USER_SERVICE, contextId = "storeStatistics", fallback = StoreStatisticsFallback.class)
public interface StoreStatisticsClient {


    /**
     * 获取待审核店铺数量
     *
     * @return 待审核店铺数量
     */
    @GetMapping("/feign/store/statistics/auditNum")
    long auditNum();

    /**
     * 获取所有店铺数量
     *
     * @return 店铺总数
     */
    @GetMapping("/feign/store/statistics/storeNum")
    long storeNum();

    /**
     * 获取今天的店铺数量
     *
     * @return 今天的店铺数量
     */
    @GetMapping("/feign/store/statistics/todayStoreNum")
    long todayStoreNum();


}
