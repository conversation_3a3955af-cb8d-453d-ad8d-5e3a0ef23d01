package plus.qdt.modules.message.fallback;

import plus.qdt.common.exception.ServiceException;
import plus.qdt.modules.message.client.WechatMessageClient;
import org.springframework.stereotype.Component;

/**
 * @author: ftyy
 * @date: 2022-01-14 9:48
 * @description: 微信Fallback
 */
public class WechatMessageFallback implements WechatMessageClient {
  @Override
  public void init() {
    throw new ServiceException();
  }

  @Override
  public void sendWechatMessage(String sn) {
    throw new ServiceException();
  }

  @Override
  public void wechatMpMessage(String sn) {
    throw new ServiceException();
  }
}
