package plus.qdt.modules.message.entity.dos;

import plus.qdt.mybatis.model.BaseStandardEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 通知类站内信模版对象
 *
 * <AUTHOR>
 * @version v4.1
 * @since 2020/12/8 9:46
 */
@Data
@TableName("li_notice_message")
@Schema(title = "通知类消息模板")
public class NoticeMessage extends BaseStandardEntity {

    private static final long serialVersionUID = 1L;

    @Schema(title = "站内信节点")
    @NotEmpty(message = "站内信节点不能为空")
    @Length(max = 50, message = "站内信节点名称太长,不能超过50")
    private String noticeNode;

    @Schema(title = "站内信标题")
    @NotEmpty(message = "站内信标题不能为空")
    @Length(max = 50, message = "站内信标题名称太长,不能超过50")
    private String noticeTitle;

    @Schema(title = "站内信内容")
    @NotEmpty(message = "站内信内容不能为空")
    @Length(max = 200, message = "站内信内容名称太长，不能超过200")
    private String noticeContent;
    /**
     * @see plus.qdt.common.enums.SwitchEnum
     */
    @NotEmpty(message = "站内信状态不能为空")
    @Schema(title = "站内信是否开启")
    private String noticeStatus;
    /**
     * @see plus.qdt.modules.message.entity.enums.NoticeMessageParameterEnum
     */
    @Schema(title = "消息变量")
    @NotEmpty(message = "站内信状态不能为空")
    private String variable;


}