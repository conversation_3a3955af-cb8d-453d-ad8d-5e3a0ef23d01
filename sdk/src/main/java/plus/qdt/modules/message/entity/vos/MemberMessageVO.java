package plus.qdt.modules.message.entity.vos;

import plus.qdt.modules.message.entity.dos.MemberMessage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
public class MemberMessageVO extends Page<MemberMessage> implements Serializable {

    @Serial
    private static final long serialVersionUID = 2228185617997060092L;

    @Schema(title = "未读数量")
    private Long unReadCount;


}
