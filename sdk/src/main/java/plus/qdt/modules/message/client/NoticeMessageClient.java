package plus.qdt.modules.message.client;

import plus.qdt.modules.constant.ServiceConstant;
import plus.qdt.modules.message.entity.dto.NoticeMessageDTO;
import plus.qdt.modules.message.fallback.NoticeMessageFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 通知类消息Client
 *
 * <AUTHOR>
 * @since 2022/1/12
 **/
@FeignClient(name = ServiceConstant.SYSTEM_SERVICE, contextId = "notice-message", fallback = NoticeMessageFallback.class)
public interface NoticeMessageClient {

    /**
     * 根据模板编码获取消息模板
     *
     * @param noticeMessageDTO 站内信消息
     */
    @PostMapping("/feign/notice-message")
    void noticeMessage(@RequestBody NoticeMessageDTO noticeMessageDTO);

}
