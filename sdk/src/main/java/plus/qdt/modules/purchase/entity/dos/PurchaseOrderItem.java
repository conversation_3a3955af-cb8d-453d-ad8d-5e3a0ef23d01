package plus.qdt.modules.purchase.entity.dos;

import plus.qdt.mybatis.model.BaseEntity;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 采购单子内容
 *
 * <AUTHOR>
 * @since 2020/11/26 19:32
 */
@Data
@TableName("li_purchase_order_item")
@Schema(title = "采购单子内容")
public class PurchaseOrderItem extends BaseEntity {

    @CreatedDate
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    @Schema(title = "创建时间", hidden = true)
    private Date createTime;

    @Schema(title = "采购ID")
    private String purchaseOrderId;

    @Schema(title = "商品名称")
    private String goodsName;

    @Schema(title = "数量")
    private String num;

    @Schema(title = "数量单位")
    private String goodsUnit;

    @Schema(title = "价格")
    private Double price;

    @Schema(title = "规格")
    private String specs;

    @Schema(title = "图片")
    private String images;


}
