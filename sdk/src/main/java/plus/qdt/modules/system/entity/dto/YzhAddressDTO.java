package plus.qdt.modules.system.entity.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 云中鹤地址数据传输对象
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Data
@Schema(title = "云中鹤地址数据传输对象")
public class YzhAddressDTO implements Serializable {

    @Schema(title = "地址编码")
    @JsonProperty("addressCode")
    private String addressCode;

    @Schema(title = "地址名称")
    @JsonProperty("addressName")
    private String addressName;

    @Schema(title = "地址级别", description = "1-省份，2-城市，3-区县")
    @JsonProperty("addressLevel")
    private Integer addressLevel;

    @Schema(title = "父级编码")
    @JsonProperty("parentCode")
    private String parentCode;

    @Schema(title = "是否启用")
    @JsonProperty("enabled")
    private Boolean enabled;

    @Schema(title = "排序")
    @JsonProperty("sort")
    private Integer sort;

    @Schema(title = "子级地址列表")
    @JsonProperty("children")
    private List<YzhAddressDTO> children;

    /**
     * 转换为通用地址DTO
     *
     * @return 通用地址DTO
     */
    public ThirdPartyAddressDTO toThirdPartyAddressDTO() {
        ThirdPartyAddressDTO dto = new ThirdPartyAddressDTO();
        dto.setAddressCode(this.addressCode);
        dto.setAddressName(this.addressName);
        dto.setAddressLevel(this.addressLevel);
        dto.setParentCode(this.parentCode);
        dto.setEnabled(this.enabled != null ? this.enabled : true);
        dto.setSort(this.sort);
        dto.setSupplierType("YZH");
        
        // 转换子级地址
        if (this.children != null && !this.children.isEmpty()) {
            List<ThirdPartyAddressDTO> childrenDTOs = this.children.stream()
                    .map(YzhAddressDTO::toThirdPartyAddressDTO)
                    .toList();
            dto.setChildren(childrenDTOs);
        }
        
        return dto;
    }
}

/**
 * 云中鹤地址API响应对象
 */
@Data
@Schema(title = "云中鹤地址API响应对象")
class YzhAddressResponse implements Serializable {

    @Schema(title = "响应码")
    @JsonProperty("code")
    private Integer code;

    @Schema(title = "响应消息")
    @JsonProperty("message")
    private String message;

    @Schema(title = "是否成功")
    @JsonProperty("success")
    private Boolean success;

    @Schema(title = "地址数据")
    @JsonProperty("data")
    private List<YzhAddressDTO> data;

    /**
     * 检查响应是否成功
     *
     * @return 是否成功
     */
    public boolean isSuccess() {
        return success != null && success && code != null && code == 200;
    }
}
