package plus.qdt.modules.system.client;

import plus.qdt.modules.constant.ServiceConstant;
import plus.qdt.modules.system.fallback.SensitiveWordsFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * 敏感词Client
 *
 * <AUTHOR>
 * @since 2021/12/17
 **/
@FeignClient(name = ServiceConstant.SYSTEM_SERVICE, contextId = "sensitive-words", fallback = SensitiveWordsFallback.class)
public interface SensitiveWordsClient {

    /**
     * 重新写入缓存
     */
    @GetMapping("/feign/sensitive-words/reset-cache")
    void resetCache();

}

