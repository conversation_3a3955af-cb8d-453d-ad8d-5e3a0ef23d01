package plus.qdt.modules.system.client;

import plus.qdt.modules.constant.ServiceConstant;
import plus.qdt.modules.system.entity.params.SmsParams;
import plus.qdt.modules.system.fallback.SmsFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 短信Client
 * <AUTHOR>
 * @since 2022/1/13
 **/
@FeignClient(name = ServiceConstant.SYSTEM_SERVICE, contextId = "sms",fallback = SmsFallback.class)
public interface SmsClient {

    /**
     * 验证码发送
     * @param smsParams
     */
    @PostMapping("/feign/sms/send")
    void sendSmsCode(@RequestBody SmsParams smsParams);

    /**
     * 验证码验证
     * @param smsParams
     * @return
     */
    @PostMapping("/feign/sms/verify")
    boolean verifyCode(@RequestBody SmsParams smsParams);

    /**
     * 短信发送
     * @param smsParams
     * @param templateCode
     */
    @PostMapping("/feign/sms/send/code/{templateCode}")
    void sendSmsCode(@RequestBody SmsParams smsParams,@PathVariable String templateCode);

    /**
     * 短信批量发送
     * @param smsParams
     */
    @PostMapping("/feign/sms/send/batch")
    void sendBatchSms(@RequestBody SmsParams smsParams);



}
