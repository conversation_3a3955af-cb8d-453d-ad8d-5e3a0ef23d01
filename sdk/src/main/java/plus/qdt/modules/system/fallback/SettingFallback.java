package plus.qdt.modules.system.fallback;

import plus.qdt.common.exception.ServiceException;
import plus.qdt.modules.system.client.SettingClient;
import plus.qdt.modules.system.entity.dos.Setting;
import org.springframework.stereotype.Component;

/**
 * SettingServiceFallback
 *
 * <AUTHOR>
 * @version v1.0
 * 2021-11-17 14:46
 */
public class SettingFallback implements SettingClient {
    @Override
    public Setting get(String key) {
        throw new ServiceException();
    }
}
