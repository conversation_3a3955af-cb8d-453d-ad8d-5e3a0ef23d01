package plus.qdt.modules.system.client;

import plus.qdt.modules.constant.ServiceConstant;
import plus.qdt.modules.search.entity.dos.HotWordsHistory;
import plus.qdt.modules.system.fallback.HotWordsHistoryFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/7/1
 **/
@FeignClient(name = ServiceConstant.SYSTEM_SERVICE, contextId = "hot-words-history", fallback = HotWordsHistoryFallback.class)
public interface HotWordsHistoryClient {


    @PostMapping("/feign/system/hot-words-history/hotWordsHistories")
    boolean saveBatch(@RequestBody List<HotWordsHistory> hotWordsHistories);

}
