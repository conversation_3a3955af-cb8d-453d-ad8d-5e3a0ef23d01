package plus.qdt.modules.system.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 基础设置
 *
 * <AUTHOR>
 * @since 2020/11/17 7:58 下午
 */
@Data
public class BaseSetting implements Serializable {

    private static final long serialVersionUID = -3138023944444671722L;

    @Schema(title = "站点名称")
    private String siteName;

    @Schema(title = "icp")
    private String icp;

    @Schema(title = "后端logo")
    private String domainLogo;

    @Schema(title = "后端icon")
    private String domainIcon;

    @Schema(title = "买家端logo")
    private String buyerSideLogo;

    @Schema(title = "买家端icon")
    private String buyerSideIcon;

    @Schema(title = "商家端logo")
    private String storeSideLogo;

    @Schema(title = "商家端icon")
    private String storeSideIcon;

    @Schema(title = "供应商端logo")
    private String supplierSideLogo;

    @Schema(title = "供应商端icon")
    private String supplierSideIcon;
}
