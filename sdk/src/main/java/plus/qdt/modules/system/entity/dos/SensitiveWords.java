package plus.qdt.modules.system.entity.dos;

import plus.qdt.mybatis.model.BaseStandardEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 敏感词实体
 * <AUTHOR>
 * 2020-02-25 14:10:16
 */
@Data
@TableName("li_sensitive_words")
@Schema(title = "敏感词")
@EqualsAndHashCode(callSuper = true)
public class SensitiveWords extends BaseStandardEntity {

    private static final long serialVersionUID = 9073293885035622239L;

    /**
     * 敏感词名称
     */
    @Schema(title = "敏感词名称")
    @NotEmpty(message = "敏感词必填")
    @Size(min = 2, max = 20, message ="敏感词需要在2-20个字符之间")
    private String sensitiveWord;

}