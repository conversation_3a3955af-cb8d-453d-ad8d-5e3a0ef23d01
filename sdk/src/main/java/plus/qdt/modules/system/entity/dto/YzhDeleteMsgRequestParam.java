package plus.qdt.modules.system.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 云中鹤删除消息请求参数
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "云中鹤删除消息请求参数")
public class YzhDeleteMsgRequestParam {

    @NotBlank(message = "接口授权访问令牌不能为空")
    @Schema(description = "接口授权访问令牌", required = true)
    private String accessToken;

    @NotEmpty(message = "需要删除的消息集合不能为空")
    @Schema(description = "需要删除的消息集合", required = true)
    private List<YzhDeleteMsgItem> list;

    /**
     * 删除消息项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "删除消息项")
    public static class YzhDeleteMsgItem {

        @NotBlank(message = "消息ID不能为空")
        @Schema(description = "消息ID", required = true)
        private String id;

        @NotBlank(message = "消息类型不能为空")
        @Schema(description = "消息类型：1：商品消息 2：销售订单消息 3：售后订单消息", required = true)
        private String messageType;
    }
}
