package plus.qdt.modules.system.client;

import plus.qdt.modules.constant.ServiceConstant;
import plus.qdt.modules.system.entity.dos.Logistics;
import plus.qdt.modules.system.entity.vo.Traces;
import plus.qdt.modules.system.fallback.LogisticsFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 物流配置客户端
 *
 * <AUTHOR>
 * @version v1.0
 * 2021-11-08 09:52
 */
@FeignClient(name = ServiceConstant.SYSTEM_SERVICE, contextId = "logistic", fallback = LogisticsFallback.class)
public interface LogisticsClient {


    /**
     * 根据id获取物流配置
     *
     * @return 全部物流
     */
    @GetMapping("/feign/logistics/all")
    List<Logistics> list();

    /**
     * 根据id获取物流配置
     *
     * @return
     */
    @GetMapping("/feign/logistics/query")
    Logistics getById(@RequestParam String logisticsId);

    /**
     * 查看物流详情
     *
     * @return
     */
    @GetMapping("/feign/logistics/query/traces")
    Traces getLogistic(@RequestParam(required = false) String mLogisticsCode,
                       @RequestParam(required = false) String mLogisticsNo,
                       @RequestParam(required = false) String customerName);
}
