package plus.qdt.modules.system.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 云中鹤删除消息响应结果
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "云中鹤删除消息响应结果")
public class YzhDeleteMsgResponse extends YzhNoticeBaseResponse {

    /**
     * 创建成功响应
     *
     * @param message 成功消息
     * @return 成功响应
     */
    public static YzhDeleteMsgResponse success(String message) {
        return YzhDeleteMsgResponse.builder()
                .success(true)
                .code("00000")
                .desc(message != null ? message : "删除消息成功")
                .result(null)
                .time(System.currentTimeMillis())
                .build();
    }

    /**
     * 创建失败响应
     *
     * @param errorCode 错误码
     * @param errorMessage 错误消息
     * @return 失败响应
     */
    public static YzhDeleteMsgResponse error(String errorCode, String errorMessage) {
        return YzhDeleteMsgResponse.builder()
                .success(false)
                .code(errorCode != null ? errorCode : "10001")
                .desc(errorMessage != null ? errorMessage : "删除消息失败")
                .result(null)
                .time(System.currentTimeMillis())
                .build();
    }
}
