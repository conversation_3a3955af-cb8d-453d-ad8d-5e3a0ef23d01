package plus.qdt.modules.system.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 云中鹤查询订单物流请求参数
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "云中鹤查询订单物流请求参数")
public class YzhQueryOrderLogisticsRequestParam {

    @Schema(description = "客户方订单编号")
    private String tparOrderCode;

    @Schema(description = "云中鹤父订单编号")
    private String parentOrderCode;

    // accessToken由系统内部设置，不需要用户传入
    private String accessToken;
}
