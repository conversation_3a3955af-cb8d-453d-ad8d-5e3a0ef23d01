package plus.qdt.modules.system.entity.enums;

/**
 * 分销模式设置
 *
 * <AUTHOR>
 * @since 2020/9/11 17:03
 */
public enum DistributionSettingEnum {

    NEW("新用户"),
    ALL("所有用户"),
    EXP("有效期"),
    FOREVER("永久");

    private String description;

    DistributionSettingEnum(String description) {
        this.description = description;
    }

    public String description() {
        return description;
    }
}
