package plus.qdt.modules.system.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 积分签到设置
 *
 * <AUTHOR>
 * @since 2021-02-26 11:48
 */
@Data
public class HotWordsSettingItem implements Comparable<HotWordsSettingItem>, Serializable {


    @Schema(title = "热词")
    private String keywords;


    @Schema(title = "默认分数")
    private Integer score;


    public Integer getScore() {
        if (score == null || score < 0) {
            return 0;
        }
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    @Override
    public int compareTo(HotWordsSettingItem pointSettingItem) {
        return pointSettingItem.getScore();
    }
}
