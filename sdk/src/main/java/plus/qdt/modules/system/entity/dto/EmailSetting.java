package plus.qdt.modules.system.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 邮箱设置
 *
 * <AUTHOR>
 * @since 2020/11/26 15:58
 */
@Data
public class EmailSetting implements Serializable {

    private static final long serialVersionUID = 7261037221941716140L;
    @Schema(title = "邮箱服务器")
    private String host;

    @Schema(title = "发送者邮箱账号")
    private String username;

    @Schema(title = "邮箱授权码")
    private String password;
}
