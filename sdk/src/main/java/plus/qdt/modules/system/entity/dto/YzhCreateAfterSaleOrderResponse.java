package plus.qdt.modules.system.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 云中鹤创建售后订单响应结果
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "云中鹤创建售后订单响应结果")
public class YzhCreateAfterSaleOrderResponse {

    @Schema(description = "接口成功与否标识")
    private Boolean success;

    @Schema(description = "接口业务编码")
    private String code;

    @Schema(description = "接口业务描述")
    private String desc;

    @Schema(description = "接口业务数据对象")
    private YzhAfterSaleOrderResult result;

    @Schema(description = "接口响应的时间戳")
    private Long time;

    /**
     * 售后订单业务数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "云中鹤售后订单业务数据")
    public static class YzhAfterSaleOrderResult {

        @Schema(description = "云中鹤(子)订单编号")
        private String orderCode;

        @Schema(description = "云中鹤售后订单编号")
        private String returnOrderCode;

        @Schema(description = "客户方售后订单编号")
        private String tparReturnOrderCode;
    }
}
