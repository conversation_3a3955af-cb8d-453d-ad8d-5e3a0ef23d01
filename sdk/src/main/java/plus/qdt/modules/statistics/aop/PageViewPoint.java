package plus.qdt.modules.statistics.aop;

import plus.qdt.modules.statistics.aop.enums.PageViewEnum;

import java.lang.annotation.*;

/**
 * 埋点统计
 *
 * <AUTHOR>
 * @since 2021/7/9 1:47 上午
 */
@Target({ElementType.PARAMETER, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface PageViewPoint {

    /**
     * 描述
     */
    PageViewEnum type();

    /**
     * 如：商品id，店铺id
     * 字段类型为string ，支持 spel语法，也可以填写
     */
    String id();
}