package plus.qdt.modules.statistics.client;

import plus.qdt.modules.constant.ServiceConstant;
import plus.qdt.modules.statistics.entity.dos.PlatformViewData;
import plus.qdt.modules.statistics.fallback.PlatformViewFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/1/17
 **/

@FeignClient(name = ServiceConstant.STATISTICS_SERVICE, contextId = "platform-view", fallback = PlatformViewFallback.class)
public interface PlatformViewClient {


    /**
     * 批量保存
     *
     * @param platformViewDataList 平台pv统计集合
     * @return 是否操作成功
     */
    @PostMapping("/feign/platform-view/save/batch")
    boolean saveBatch(@RequestBody List<PlatformViewData> platformViewDataList);


}
