package plus.qdt.modules.statistics.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 消息提示
 *
 * <AUTHOR>
 * @since 2020/12/9 14:25
 */
@Data
public class IndexNoticeVO {

    @Schema(title = "待处理商品审核")
    private Long goods;

    @Schema(title = "待处理店铺入驻审核")
    private Long store;

    @Schema(title = "待处理售后申请")
    private Long refund;

    @Schema(title = "待处理投诉审核")
    private Long complain;

    @Schema(title = "待处理分销员提现申请")
    private Long distributionCash;

    @Schema(title = "待处理商家结算")
    private Long waitPayBill;

}
