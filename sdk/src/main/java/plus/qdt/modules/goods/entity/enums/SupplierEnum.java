package plus.qdt.modules.goods.entity.enums;

/**
 * 供应商枚举
 *
 * <AUTHOR>
 * @since 2022/6/2
 **/
public enum SupplierEnum {

    /**
     * 供应商类型
     */
    ZKH("震坤行", "-1"),
    JD("京东", "-2"),
    CUSTOM("自行注册的供应商", "0"),
    YZH("云中鹤", "1"),
    ;

    private final String description;
    private final String defaultId;

    SupplierEnum(String description, String defaultId) {
        this.description = description;
        this.defaultId = defaultId;
    }

    /**
     * 根据店铺ID获取供应商类型
     *
     * @param storeId 店铺id
     * @return 供应商类型
     */
    public static SupplierEnum getSupplierEnum(String storeId) {
        for (SupplierEnum supplierEnum : SupplierEnum.values()) {
            if (supplierEnum.getDefaultId().equals(storeId)) {
                return supplierEnum;
            }
        }
        return CUSTOM;
    }

    public String getDescription() {
        return description;
    }

    public String getDefaultId() {
        return defaultId;
    }
}
