package plus.qdt.modules.goods.client;

import plus.qdt.modules.constant.ServiceConstant;
import plus.qdt.modules.goods.entity.dto.GoodsNumSearchParams;
import plus.qdt.modules.goods.fallback.GoodsStatisticsFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @since 2022/6/28
 **/
@FeignClient(name = ServiceConstant.GOODS_SERVICE, contextId = "goods-statistics", fallback = GoodsStatisticsFallback.class)
public interface GoodsStatisticsClient {

    /**
     * 获取所有的已上架的商品数量
     *
     * @param goodsStatusEnum 商品状态枚举
     * @param goodsAuthEnum   商品审核枚举
     * @return 所有的已上架的商品数量
     */
    @PostMapping("/feign/goods/statistics/goodsNum")
    long goodsNum(@RequestBody GoodsNumSearchParams searchParams);

    /**
     * 获取今天的已上架的商品数量
     *
     * @return 今天的已上架的商品数量
     */
    @GetMapping("/feign/goods/statistics/todayUpperNum")
    long todayUpperNum();

}
