package plus.qdt.modules.goods.entity.dos;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import plus.qdt.mybatis.model.BaseStandardEntity;

/**
 * 云中鹤分类映射历史记录实体
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@TableName("li_yzh_category_mapping_history")
@Schema(title = "云中鹤分类映射历史记录")
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class YzhCategoryMappingHistory extends BaseStandardEntity {

    @Schema(title = "映射记录ID")
    private String mappingId;

    @Schema(title = "操作类型：CREATE-创建，UPDATE-更新，DELETE-删除")
    private String operationType;

    @Schema(title = "原系统分类ID")
    private String oldSystemCategoryId;

    @Schema(title = "原系统分类名称")
    private String oldSystemCategoryName;

    @Schema(title = "新系统分类ID")
    private String newSystemCategoryId;

    @Schema(title = "新系统分类名称")
    private String newSystemCategoryName;

    @Schema(title = "操作原因")
    private String operationReason;

    /**
     * 操作类型枚举
     */
    public enum OperationType {
        CREATE("CREATE", "创建"),
        UPDATE("UPDATE", "更新"),
        DELETE("DELETE", "删除");

        private final String code;
        private final String desc;

        OperationType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
}
