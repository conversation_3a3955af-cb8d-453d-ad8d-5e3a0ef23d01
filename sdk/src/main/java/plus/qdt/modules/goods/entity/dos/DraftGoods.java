package plus.qdt.modules.goods.entity.dos;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.http.HtmlUtil;
import cn.hutool.json.JSONUtil;
import plus.qdt.modules.goods.entity.dto.GoodsOperationDTO;
import plus.qdt.modules.goods.entity.enums.GoodsAuthEnum;
import plus.qdt.modules.goods.entity.enums.GoodsMarketEnum;
import plus.qdt.modules.goods.entity.enums.SalesModeEnum;
import plus.qdt.mybatis.model.BaseSceneEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xkcoding.http.util.StringUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.springframework.beans.BeanUtils;

/**
 * 草稿商品
 *
 * <AUTHOR>
 * @since 2020-02-23 9:14:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("li_draft_goods")
@Schema(title = "草稿商品")
@AllArgsConstructor
@NoArgsConstructor
public class DraftGoods extends BaseSceneEntity {


    private static final long serialVersionUID = 466771876880180330L;


    @Schema(title = "商品名称")
    @NotEmpty(message = "商品名称不能为空")
    @Length(max = 100, message = "商品名称太长，不能超过100个字符")
    private String goodsName;

    @Schema(title = "商品价格", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "商品价格不能为空")
    @Min(value = 0, message = "商品价格不能为负数")
    @Max(value = 99999999, message = "商品价格不能超过99999999")
    private Double price;

    @Schema(title = "品牌id")
    private String brandId;

    @Schema(title = "分类path")
    private String categoryPath;

    @Schema(title = "计量单位")
    private String goodsUnit;


    @Length(max = 60, message = "商品卖点太长，不能超过60个字符")
    @Schema(title = "卖点")
    private String sellingPoint;

    /**
     * @see GoodsMarketEnum
     */
    @Schema(title = "上架状态")
    private String marketEnable;

    @Schema(title = "详情")
    private String intro;

    @Schema(title = "购买数量")
    private Integer buyCount;

    @Max(value = 99999999, message = "库存不能超过99999999")
    @Schema(title = "库存")
    private Integer quantity;

    @Schema(title = "商品好评率")
    private Double grade;

    @Schema(title = "缩略图路径")
    private String thumbnail;

    @Schema(title = "小图路径")
    private String small;

    @Schema(title = "原图路径")
    private String original;

    @Schema(title = "店铺分类id")
    private String storeCategoryPath;

    @Schema(title = "评论数量")
    private Integer commentNum;

    @Schema(title = "卖家id")
    private String storeId;

    @Schema(title = "卖家名字")
    private String storeName;

    @Schema(title = "运费模板id")
    private String templateId;

    /**
     * @see GoodsAuthEnum
     */
    @Schema(title = "审核状态")
    private String authFlag;

    @Schema(title = "审核信息")
    private String authMessage;

    @Schema(title = "下架原因")
    private String underMessage;

    @Schema(title = "是否自营")
    private Boolean selfOperated;

    @Schema(title = "商品移动端详情")
    private String mobileIntro;

    @Schema(title = "商品视频")
    private String goodsVideo;


    @Schema(title = "是否为推荐商品", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean recommend;

    /**
     * @see SalesModeEnum
     */
    @Schema(title = "销售模式", requiredMode = Schema.RequiredMode.REQUIRED)
    private String salesModel;


    /**
     * @see plus.qdt.modules.goods.entity.enums.GoodsTypeEnum
     */
    @Schema(title = "商品类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String goodsType;

    @Schema(title = "商品参数json", hidden = true)
    private String params;

    @Schema(title = "存入索引目标，为空则应该是默认索引", hidden = true)
    private String esIndex;


    @Schema(title = "供应商ID")
    private String supplierId;

    @Schema(title = "供应商名称")
    private String supplierName;

    @Schema(title = "供应商商品ID")
    private String supplierGoodsId;

    @Schema(title = "支持代发")
    private Boolean supportProxy;

    @Schema(title = "支持采购")
    private Boolean supportPurchase;

    /**
     * @see plus.qdt.modules.goods.entity.enums.PurchaseRuleEnum
     */
    @Schema(title = "采购规则")
    private String purchaseRule;

    /**
     * @see plus.qdt.modules.goods.entity.enums.SupplierEnum
     */
    @Schema(title = "供应商类型")
    private String supplierEnum;


    @Schema(title = "保留字段1", hidden = true)
    private String field1;

    @Schema(title = "保留字段2", hidden = true)
    private String field2;

    @Schema(title = "保留字段3", hidden = true)
    private String field3;

    @Schema(title = "保留字段4", hidden = true)
    private String field4;

    @Schema(title = "保留字段5", hidden = true)
    private String field5;

    @Schema(title = "扩展字段，可自由存储，数据库为text格式", hidden = true)
    private String ext;

    @Schema(title = "是否是代理商品")
    private Boolean isProxyGoods = false;

    public DraftGoods(GoodsOperationDTO goodsOperationDTO) {
        BeanUtils.copyProperties(goodsOperationDTO, this);


        if (goodsOperationDTO.getGoodsParamsDTOList() != null && goodsOperationDTO.getGoodsParamsDTOList().isEmpty()) {
            this.params = JSONUtil.toJsonStr(goodsOperationDTO.getGoodsParamsDTOList());
        }
        //如果立即上架则
        this.marketEnable = Boolean.TRUE.equals(goodsOperationDTO.getRelease()) ? GoodsMarketEnum.UPPER.name() : GoodsMarketEnum.DOWN.name();

        this.grade = 100D;

        if (StringUtil.isNotEmpty(goodsOperationDTO.getGoodsId())) {
            this.setId(goodsOperationDTO.getGoodsId());
        }

    }

    public Boolean getSupportProxy() {
        if (supportProxy == null) {
            return false;
        }
        return supportProxy;
    }

    public Boolean getSupportPurchase() {
        if (supportPurchase == null) {
            return false;
        }
        return supportPurchase;
    }

    public Boolean getIsProxyGoods() {
        if (isProxyGoods == null) {
            return false;
        }
        return isProxyGoods;
    }

    public String getIntro() {
        if (CharSequenceUtil.isNotEmpty(intro)) {
            return HtmlUtil.unescape(intro);
        }
        return intro;
    }

    public String getMobileIntro() {
        if (CharSequenceUtil.isNotEmpty(mobileIntro)) {
            return HtmlUtil.unescape(mobileIntro);
        }
        return mobileIntro;
    }

}