package plus.qdt.modules.goods.entity.dto;

import plus.qdt.common.vo.PageVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 商品品牌dto
 *
 * <AUTHOR>
 * @since 2020-02-18 15:18:56
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "商品品牌dto")
public class BrandPageDTO extends PageVO {

    @Serial
    private static final long serialVersionUID = 8906820486037326039L;

    @Schema(title = "品牌名称")
    private String name;
}
