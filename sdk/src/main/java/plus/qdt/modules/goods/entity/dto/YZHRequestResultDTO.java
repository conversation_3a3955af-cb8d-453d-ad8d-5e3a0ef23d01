package plus.qdt.modules.goods.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 云中鹤请求结果DTO
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
@Data
@NoArgsConstructor
@Schema(title = "云中鹤请求结果DTO", description = "用于接收云中鹤平台接口通用响应数据")
public class YZHRequestResultDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(title = "接口成功与否标识", description = "true或false表示接口调用成功与否")
    private Boolean success;

    @Schema(title = "接口业务编码", description = "接口调用业务编码，00000表示成功，其他表示失败")
    private String code;

    @Schema(title = "接口业务描述", description = "接口调用错误提示或成功提示")
    private String desc;

    @Schema(title = "接口业务数据对象", description = "接口返回的业务数据，格式为JSON")
    private String result;

    @Schema(title = "接口响应的时间戳", description = "接口返回数据的时间戳")
    private Long time;
}
