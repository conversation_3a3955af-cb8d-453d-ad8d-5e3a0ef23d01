package plus.qdt.modules.goods.entity.dto;

import plus.qdt.modules.goods.entity.enums.GoodsAuthEnum;
import plus.qdt.modules.goods.entity.enums.GoodsMarketEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/6/28
 **/
@Data
public class GoodsNumSearchParams {

    private GoodsMarketEnum goodsMarketEnum;
    private GoodsAuthEnum goodsAuthEnum;

    private String storeId;


    public GoodsNumSearchParams(GoodsMarketEnum goodsMarketEnum, GoodsAuthEnum goodsAuthEnum, String storeId) {
        this.goodsMarketEnum = goodsMarketEnum;
        this.goodsAuthEnum = goodsAuthEnum;
        this.storeId = storeId;
    }
}
