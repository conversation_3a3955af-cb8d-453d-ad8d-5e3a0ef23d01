package plus.qdt.modules.auth.fallback;

import plus.qdt.common.exception.ServiceException;
import plus.qdt.common.security.AuthUser;
import plus.qdt.common.security.enums.SceneEnums;
import plus.qdt.common.security.token.Token;
import plus.qdt.modules.auth.client.AuthClient;
import plus.qdt.modules.member.entity.dos.User;

/**
 * TokenGenerateFallback
 *
 * <AUTHOR>
 * @version v1.0
 * 2022-08-31 11:29
 */
public class TokenGenerateFallback implements AuthClient {
    @Override
    public Token createToken(User user, boolean longTerm) {
        throw new ServiceException();
    }

    @Override
    public Token refreshToken(String refreshToken) {
        throw new ServiceException();
    }

    @Override
    public void logout(SceneEnums sceneEnums) {
        throw new ServiceException();
    }

    @Override
    public void resetUserPermissionMap(AuthUser authUser) {
        throw new ServiceException();
    }
}
