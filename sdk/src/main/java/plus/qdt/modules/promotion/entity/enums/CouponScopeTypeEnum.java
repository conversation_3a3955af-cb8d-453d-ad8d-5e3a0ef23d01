package plus.qdt.modules.promotion.entity.enums;

/**
 * 优惠券适用范围类型枚举
 *
 * <AUTHOR>
 * @since 2020-03-19 9:36 上午
 */
public enum CouponScopeTypeEnum {

    /**
     * 枚举
     */
    ALL("全品类"),
    PORTION_GOODS_CATEGORY("部分商品分类"),
    PORTION_SHOP_CATEGORY("部分店铺分类"),
    PORTION_GOODS("指定商品");

    private final String description;

    CouponScopeTypeEnum(String str) {
        this.description = str;
    }

    public String description() {
        return description;
    }
}
