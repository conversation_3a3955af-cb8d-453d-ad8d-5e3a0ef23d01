package plus.qdt.modules.promotion.client;

import plus.qdt.cache.CachePrefix;
import plus.qdt.common.enums.PromotionTypeEnum;
import plus.qdt.modules.constant.ServiceConstant;
import plus.qdt.modules.order.cart.entity.vo.CartSkuVO;
import plus.qdt.modules.promotion.entity.dos.PromotionGoods;
import plus.qdt.modules.promotion.entity.dto.search.PromotionGoodsSearchParams;
import plus.qdt.modules.promotion.fallback.PromotionGoodsFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * @author: ftyy
 * @date: 2022-01-13 18:35
 * @description: 促销商品Client
 */
@FeignClient(name = ServiceConstant.PROMOTION_SERVICE, contextId = "promotion-goods", fallback = PromotionGoodsFallback.class)
public interface PromotionGoodsClient {

    /**
     * 缓存商品库存key
     *
     * @param typeEnum    促销分类枚举
     * @param promotionId 促销活动Id
     * @param skuId       skuId
     * @return 缓存商品库存key
     */
    @GetMapping("/feign/promotion/promotion-goods/getPromotionGoodsStockCacheKey")
    static String getPromotionGoodsStockCacheKey(@RequestParam PromotionTypeEnum typeEnum, @RequestParam String promotionId, @RequestParam String skuId) {
        return "{" + CachePrefix.PROMOTION_GOODS_STOCK.name() + "_" + typeEnum.name() + "}_" + promotionId + "_" + skuId;
    }

    @GetMapping("/feign/promotion/promotion-goods/getPromotionStockCacheKey")
    static String getPromotionStockCacheKey(
            @RequestParam PromotionTypeEnum typeEnum, @RequestParam String promotionId) {
        return "{"
                + CachePrefix.PROMOTION_GOODS_STOCK.name()
                + "_"
                + typeEnum.name()
                + "}_"
                + promotionId
                + "_";
    }

    /**
     * 获取某sku所有有效活动
     *
     * @param skuId    商品skuId
     * @param storeIds 店铺id
     * @return 促销商品集合
     */
    @GetMapping("/feign/promotion/promotion-goods/valid/promotion/{skuId}")
    List<PromotionGoods> findSkuValidPromotion(@PathVariable String skuId, @RequestParam String storeIds);

    /**
     * 获取促销商品信息
     *
     * @param searchParams 查询参数
     * @return 促销商品列表
     */
    @PostMapping("/feign/promotion/promotion-goods/listFindAll")
    List<PromotionGoods> listFindAll(@RequestBody PromotionGoodsSearchParams searchParams);

    /**
     * 获取促销商品信息
     *
     * @param searchParams 查询参数
     * @return 促销商品信息
     */
    @PostMapping("/feign/promotion/promotion-goods/getPromotionsGoods")
    PromotionGoods getPromotionsGoods(@RequestBody PromotionGoodsSearchParams searchParams);

    /**
     * 获取当前有效时间特定促销类型的促销商品信息
     *
     * @param skuId          skuId
     * @param promotionTypes 特定促销类型
     * @return 促销商品信息
     */
    @PostMapping("/feign/promotion/promotion-goods/{skuId}/valid")
    PromotionGoods getValidPromotionsGoods(@PathVariable String skuId, @RequestBody List<String> promotionTypes);

    /**
     * 获取当前有效时间特定促销类型的促销商品价格
     *
     * @param skuId          skuId
     * @param promotionTypes 特定促销类型
     * @return 促销商品价格
     */
    @PostMapping("/feign/promotion/promotion-goods/{skuId}/valid/price")
    Double getValidPromotionsGoodsPrice(@PathVariable String skuId, @RequestBody List<String> promotionTypes);

    /**
     * 查询参加活动促销商品是否同时参加指定类型的活动
     *
     * @param promotionType 促销类型
     * @param skuId         skuId
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @param promotionId   促销活动id(是否排除当前活动，如排除，则填写，没有的话，为null)
     * @return 共参加了几种活动
     */
    @GetMapping("/feign/promotion/promotion-goods/inner-overlap/{skuId}")
    Integer findInnerOverlapPromotionGoods(@RequestParam String promotionType, @PathVariable String skuId, @RequestParam Date startTime, @RequestParam Date endTime, @RequestParam String promotionId);

    /**
     * 获取促销活动商品库存
     *
     * @param typeEnum    促销商品类型
     * @param promotionId 促销活动id
     * @param skuId       商品skuId
     * @return 促销活动商品库存
     */
    @GetMapping("/feign/promotion/promotion-goods/{promotionId}/{skuId}/stock")
    Integer getPromotionGoodsStock(@RequestParam PromotionTypeEnum typeEnum, @PathVariable String promotionId, @PathVariable String skuId);

    /**
     * 批量获取促销活动商品库存
     *
     * @param typeEnum    促销商品类型
     * @param promotionId 促销活动id
     * @param skuId       批量商品skuId
     * @return 促销活动商品库存
     */
    @GetMapping("/feign/promotion/promotion-goods/{promotionId}")
    List<Integer> getPromotionGoodsStock(@RequestParam PromotionTypeEnum typeEnum, @PathVariable String promotionId, @RequestParam List<String> skuId);

    /**
     * 更新促销活动商品库存
     *
     * @param promotionGoodsList 更新促销活动商品信息
     */
    @PutMapping("/feign/promotion/promotion-goods/stock")
    void updatePromotionGoodsStock(@RequestBody List<PromotionGoods> promotionGoodsList);

    /**
     * 更新促销活动商品索引
     *
     * @param promotionGoods 促销商品信息
     */
    @PutMapping("/feign/promotion/promotion-goods/updatePromotionGoodsByPromotions")
    void updatePromotionGoodsByPromotions(@RequestBody PromotionGoods promotionGoods);

    /**
     * 删除促销商品
     *
     * @param promotionId 促销活动id
     * @param skuIds      skuId
     */
    @DeleteMapping("/feign/promotion/promotion-goods/{promotionId}")
    void deletePromotionGoods(@PathVariable String promotionId, @RequestBody List<String> skuIds);

    /**
     * 删除促销促销商品
     *
     * @param promotionIds 促销活动id
     */
    @DeleteMapping("/feign/promotion/promotion-goods/deletePromotionGoodsIds")
    void deletePromotionGoods(@RequestBody List<String> promotionIds);

    /**
     * 根据参数删除促销商品
     *
     * @param searchParams 查询参数
     */
    @DeleteMapping("/feign/promotion/promotion-goods/deletePromotionGoods")
    void deletePromotionGoods(@RequestBody PromotionGoodsSearchParams searchParams);

    @PutMapping("/feign/promotion/promotion-goods/updateBatchById")
    void updateBatchById(@RequestBody List<PromotionGoods> promotionGoods);

    /**
     * 获取当前商品促销信息
     *
     * @param cartSkuVO 购物车商品信息
     * @return 当前商品促销信息
     */
    @PutMapping("/feign/promotion/promotion-goods/getCurrentGoodsPromotion")
    CartSkuVO getCurrentGoodsPromotion(@RequestBody CartSkuVO cartSkuVO);

    /**
     * 更新促销活动商品库存
     *
     * @param skuId    商品skuId
     * @param quantity 库存
     */
    @PutMapping("/feign/promotion/promotion-goods/stock/quantity")
    void updatePromotionGoodsStock(@RequestParam String skuId, @RequestParam Integer quantity);
}
