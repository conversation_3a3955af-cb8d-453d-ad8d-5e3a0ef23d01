package plus.qdt.modules.promotion.entity.dto.search;

import cn.hutool.core.text.CharSequenceUtil;
import plus.qdt.modules.promotion.entity.enums.CouponGetEnum;
import plus.qdt.modules.promotion.entity.enums.CouponTypeEnum;
import plus.qdt.modules.promotion.entity.enums.MemberCouponStatusEnum;
import plus.qdt.modules.promotion.entity.enums.PromotionsScopeTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 会员优惠券查询通用类
 *
 * <AUTHOR>
 * @since 2020/8/14
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class MemberCouponSearchParams extends BasePromotionsSearchParams implements Serializable {

    @Serial
    private static final long serialVersionUID = 4566880169478260409L;

    private static final String PRICE_COLUMN = "price";

    @Schema(title = "优惠券id")
    private String couponId;

    @Schema(title = "优惠券名称")
    private String couponName;

    @Schema(title = "会员id")
    private String memberId;

    @Schema(title = "会员名称")
    private String memberName;

    /**
     * POINT("打折"), PRICE("减免现金");
     *
     * @see CouponTypeEnum
     */
    @Schema(title = "活动类型")
    private String couponType;

    @Schema(title = "面额,可以为范围，如10_1000")
    private String price;
    /**
     * @see CouponGetEnum
     */
    @Schema(title = "优惠券类型，分为免费领取和活动赠送")
    private String getType;
    /**
     * @see MemberCouponStatusEnum
     */
    @Schema(title = "会员优惠券状态")
    private String memberCouponStatus;
    @Schema(title = "消费门槛")
    private Double consumeThreshold;


    @Override
    public <T> QueryWrapper<T> queryWrapper() {
        QueryWrapper<T> queryWrapper = super.queryWrapper();
        if (CharSequenceUtil.isNotEmpty(couponType)) {
            queryWrapper.eq("coupon_type", CouponTypeEnum.valueOf(couponType).name());
        }
        if (memberId != null) {
            queryWrapper.eq("member_id", memberId);
        }
        if (CharSequenceUtil.isNotEmpty(getType)) {
            queryWrapper.eq("get_type", CouponGetEnum.valueOf(getType).name());
        }
        if (CharSequenceUtil.isNotEmpty(memberCouponStatus)) {
            queryWrapper.eq("member_coupon_status", MemberCouponStatusEnum.valueOf(memberCouponStatus).name());
        }
        if (CharSequenceUtil.isNotEmpty(price)) {
            String[] s = price.split("_");
            if (s.length > 1) {
                queryWrapper.between(PRICE_COLUMN, s[0], s[1]);
            } else {
                queryWrapper.ge(PRICE_COLUMN, s[0]);
            }
        }
        if (this.getStartTime() != null) {
            queryWrapper.ge("start_time", new Date(this.getEndTime()));
        }
        if (this.getEndTime() != null) {
            queryWrapper.le("end_time", new Date(this.getEndTime()));
        }
        queryWrapper.eq("delete_flag", false);
        queryWrapper.orderByDesc("create_time");
        return queryWrapper;
    }

    public <T> QueryWrapper<T> queryWrapperWithCoupon() {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(couponId), "mc.coupon_id", couponId);
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(couponName), "mc.coupon_name", couponName);
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(memberName), "mc.member_name", memberName);
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(memberId), "mc.member_id", memberId);
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(this.getStoreId()), "c.store_id", this.getExtendId());
        if (CharSequenceUtil.isNotEmpty(couponType)) {
            queryWrapper.eq("mc.coupon_type", CouponTypeEnum.valueOf(couponType).name());
        }
        if (CharSequenceUtil.isNotEmpty(this.getScopeId())) {
            queryWrapper.eq("mc.scope_id", this.getScopeId());
        }
        if (CharSequenceUtil.isNotEmpty(this.getScopeType())) {
            queryWrapper.eq("mc.scope_type", PromotionsScopeTypeEnum.valueOf(this.getScopeType()).name());
        }
        if (CharSequenceUtil.isNotEmpty(getType)) {
            queryWrapper.eq("mc.get_type", CouponGetEnum.valueOf(getType).name());
        }
        if (CharSequenceUtil.isNotEmpty(memberCouponStatus)) {
            queryWrapper.eq("mc.member_coupon_status", MemberCouponStatusEnum.valueOf(memberCouponStatus).name());
        }
        if (CharSequenceUtil.isNotEmpty(price)) {
            String[] s = price.split("_");
            if (s.length > 1) {
                queryWrapper.between("mc.price", s[0], s[1]);
            } else {
                queryWrapper.ge("mc.price", s[0]);
            }
        }
        if (this.getStartTime() != null) {
            queryWrapper.ge("mc.start_time", new Date(this.getEndTime()));
        }
        if (this.getEndTime() != null) {
            queryWrapper.le("mc.end_time", new Date(this.getEndTime()));
        }
        queryWrapper.eq("mc.delete_flag", false);
        queryWrapper.orderByDesc("mc.create_time");
        return queryWrapper;
    }

}
