package plus.qdt.modules.promotion.entity.dto;

import plus.qdt.modules.goods.entity.dos.GoodsSku;
import plus.qdt.modules.promotion.entity.dos.PromotionGoods;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * 促销商品数据传输对象
 *
 * <AUTHOR>
 * @since 2020/10/9
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class PromotionGoodsDTO extends PromotionGoods {

    private static final long serialVersionUID = 9206970681612883421L;

    @Schema(title = "商品id")
    private String goodsId;

    @Schema(title = "商品名称")
    private String goodsName;

    @Schema(title = "商品图片")
    private String goodsImage;

    public PromotionGoodsDTO(GoodsSku sku) {
        super(sku);
    }
}
