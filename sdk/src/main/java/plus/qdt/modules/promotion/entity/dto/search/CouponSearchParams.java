package plus.qdt.modules.promotion.entity.dto.search;

import cn.hutool.core.text.CharSequenceUtil;
import plus.qdt.common.security.AuthUser;
import plus.qdt.common.security.context.UserContext;
import plus.qdt.common.security.enums.SceneEnums;
import plus.qdt.modules.promotion.entity.enums.CouponGetEnum;
import plus.qdt.modules.promotion.entity.enums.CouponRangeDayEnum;
import plus.qdt.modules.promotion.entity.enums.CouponTypeEnum;
import plus.qdt.modules.promotion.entity.enums.PromotionsStatusEnum;
import plus.qdt.modules.promotion.tools.PromotionTools;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 优惠券查询通用类
 *
 * <AUTHOR>
 * @since 2020/8/14
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class CouponSearchParams extends BasePromotionsSearchParams implements Serializable {

    @Serial
    private static final long serialVersionUID = 4566880169478260409L;

    private static final String PRICE_COLUMN = "price";
    private static final String RANGE_DAY_TYPE_COLUMN = "range_day_type";

    @Schema(title = "会员id")
    private String memberId;

    @Schema(title = "优惠券名称")
    private String couponName;
    /**
     * POINT("打折"), PRICE("减免现金");
     *
     * @see plus.qdt.modules.promotion.entity.enums.CouponTypeEnum
     */
    @Schema(title = "活动类型")
    private String couponType;

    @Schema(title = "面额,可以为范围，如10_1000")
    private String price;

    @Schema(title = "发行数量,可以为范围，如10_1000")
    private String publishNum;

    @Schema(title = "已被领取的数量,可以为范围，如10_1000")
    private String receivedNum;

    /**
     * @see plus.qdt.modules.promotion.entity.enums.CouponGetEnum
     */
    @Schema(title = "优惠券类型，分为免费领取和活动赠送")
    private String getType;


    @Override
    public <T> QueryWrapper<T> queryWrapper() {
        QueryWrapper<T> queryWrapper = super.baseQueryWrapperWithAuth();
        if (CharSequenceUtil.isNotEmpty(couponName)) {
            queryWrapper.eq("coupon_name", couponName);
        }
        if (memberId != null) {
            queryWrapper.eq("member_id", memberId);
        }
        if (CharSequenceUtil.isNotEmpty(couponType)) {
            queryWrapper.eq("coupon_type", CouponTypeEnum.valueOf(couponType).name());
        }
        if (CharSequenceUtil.isNotEmpty(getType)) {
            queryWrapper.eq("get_type", CouponGetEnum.valueOf(getType).name());
        }
        if (CharSequenceUtil.isNotEmpty(this.getPromotionStatus())) {
            queryWrapper.and(p -> {
                switch (PromotionsStatusEnum.valueOf(this.getPromotionStatus())) {
                    case NEW -> p.nested(i -> i.gt(PromotionTools.START_TIME_COLUMN, new Date()).gt(PromotionTools.END_TIME_COLUMN, new Date()));
                    case START -> p.nested(i -> i.le(PromotionTools.START_TIME_COLUMN, new Date()).ge(PromotionTools.END_TIME_COLUMN, new Date()))
                            .or(i -> i.gt("effective_days", 0).eq(RANGE_DAY_TYPE_COLUMN, CouponRangeDayEnum.DYNAMICTIME.name()));
                    case END -> p.nested(i -> i.lt(PromotionTools.START_TIME_COLUMN, new Date()).lt(PromotionTools.END_TIME_COLUMN, new Date()));
                    case CLOSE -> p.nested(n -> n.nested(i -> i.isNull(PromotionTools.START_TIME_COLUMN).isNull(PromotionTools.END_TIME_COLUMN)
                                    .eq(RANGE_DAY_TYPE_COLUMN, CouponRangeDayEnum.FIXEDTIME.name())).
                            or(i -> i.le("effective_days", 0).eq(RANGE_DAY_TYPE_COLUMN, CouponRangeDayEnum.DYNAMICTIME.name())));
                }
            });

        } else {
            // 用户
            AuthUser authUser = UserContext.getCurrentUser();
            if (authUser != null && authUser.getScene().equals(SceneEnums.MEMBER)) {
                queryWrapper.and(p -> p.nested(i -> i.le(PromotionTools.START_TIME_COLUMN, new Date()).ge(PromotionTools.END_TIME_COLUMN, new Date()))
                        .or(i -> i.gt("effective_days", 0).eq(RANGE_DAY_TYPE_COLUMN, CouponRangeDayEnum.DYNAMICTIME.name())));
            }
        }
        this.betweenWrapper(queryWrapper);
        queryWrapper.orderByDesc("create_time");
        return queryWrapper;
    }

    private <T> void betweenWrapper(QueryWrapper<T> queryWrapper) {
        if (CharSequenceUtil.isNotEmpty(publishNum)) {
            String[] s = publishNum.split("_");
            if (s.length > 1) {
                queryWrapper.between("publish_num", s[0], s[1]);
            } else {
                queryWrapper.ge("publish_num", s[0]);
            }
        }
        if (CharSequenceUtil.isNotEmpty(price)) {
            String[] s = price.split("_");
            if (s.length > 1) {
                queryWrapper.between(PRICE_COLUMN, s[0], s[1]);
            } else {
                queryWrapper.ge(PRICE_COLUMN, s[0]);
            }
        }
        if (CharSequenceUtil.isNotEmpty(receivedNum)) {
            String[] s = receivedNum.split("_");
            if (s.length > 1) {
                queryWrapper.between("received_num", s[0], s[1]);
            } else {
                queryWrapper.ge("received_num", s[0]);
            }
        }
    }

}
