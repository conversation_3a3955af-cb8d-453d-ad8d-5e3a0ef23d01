package plus.qdt.modules.promotion.client;

import plus.qdt.modules.constant.ServiceConstant;
import plus.qdt.modules.promotion.fallback.MemberCouponFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @author: ftyy
 * @date: 2022-01-13 17:40
 * @description: 会员优惠券Client
 */
@FeignClient(name = ServiceConstant.PROMOTION_SERVICE, contextId = "member-coupon", fallback = MemberCouponFallback.class)
public interface MemberCouponClient {

    /**
     * 领取优惠券
     *
     * @param couponId   优惠券编号
     * @param memberId   会员
     * @param memberName 会员名称
     */
    @GetMapping("/feign/promotion/member-coupon/receiveCoupon")
    void receiveCoupon(@RequestParam String couponId, @RequestParam String memberId, @RequestParam String memberName);

    /**
     * 使用优惠券
     *
     * @param ids 会员优惠券id
     */
    @PutMapping("/feign/promotion/member-coupon/{memberId}/used")
    void used(@PathVariable String memberId, @RequestBody List<String> ids);

    /**
     * 作废无效的会员优惠券
     *
     * @return 是否操作成功
     */
    @PutMapping("/feign/promotion/member-coupon/expire/invalid")
    boolean expireInvalidMemberCoupon();

    /**
     * 作废无效的会员优惠券
     *
     * @param memberId 会员id
     * @return 是否操作成功
     */
    @PutMapping("/feign/promotion/member-coupon/expire/invalid/{memberId}")
    boolean expireInvalidMemberCoupon(@PathVariable String memberId, String couponId);


    /**
     * 删除作废/过期的会员优惠券
     *
     * @param expirationDay 过期常量，过期后或者使用后一定时间内，删除无效的优惠券，物理删除
     * @return 是否操作成功
     */
    @PutMapping("/feign/promotion/member-coupon/expire/remove")
    boolean removeInvalidMemberCoupon(@RequestParam int expirationDay);

    /**
     * 清空无效领取优惠券标记
     */
    @PutMapping("/feign/promotion/member-coupon/sign/clean")
    void cleanMemberCouponSign();
    /**
     * 恢复会员优惠券
     *
     * @param memberCouponIds 会员优惠券id列表
     * @return 是否恢复成功
     */
    @PutMapping("/feign/promotion/member-coupon/recovery")
    boolean recoveryMemberCoupon(@RequestBody List<String> memberCouponIds);

    /**
     * 获取会员领取过的优惠券数量
     */
    @GetMapping("/feign/promotion/member-coupon/count")
    Long getMemberCouponCount(@RequestParam String memberId, @RequestParam String couponId);
}

