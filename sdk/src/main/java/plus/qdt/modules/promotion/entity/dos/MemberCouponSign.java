package plus.qdt.modules.promotion.entity.dos;

import plus.qdt.mybatis.model.BaseEntity;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 会员优惠券实体类
 *
 * <AUTHOR>
 * @since 2020-03-19 10:44 上午
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("li_member_coupon_sign")
@Schema(title = "会员优惠券领取标记")
public class MemberCouponSign extends BaseEntity {

    private static final long serialVersionUID = 8901202819626621215L;

    @Schema(title = "优惠券活动ID")
    private String couponActivityId;

    @Schema(title = "会员ID")
    private String memberId;

    @Schema(title = "失效时间，到达失效时间后自动删除，用户可以再次领取")
    private Date invalidTime;


    @CreatedDate
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    @Schema(title = "创建时间", hidden = true)
    @Field(type = FieldType.Date, pattern = "yyyy-MM-dd HH:mm:ss || yyyy-MM-dd || yyyy/MM/dd HH:mm:ss|| yyyy/MM/dd " +
            "||epoch_millis")
    private Date createTime;

}