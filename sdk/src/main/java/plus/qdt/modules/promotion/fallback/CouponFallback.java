package plus.qdt.modules.promotion.fallback;

import plus.qdt.common.exception.ServiceException;
import plus.qdt.common.vo.PageVO;
import plus.qdt.modules.promotion.client.CouponClient;
import plus.qdt.modules.promotion.entity.dos.Coupon;
import plus.qdt.modules.promotion.entity.vos.CouponVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 优惠券活动Fallback
 */
public class CouponFallback implements CouponClient {


    @Override
    public Coupon getById(String couponId) {
        throw new ServiceException();
    }

    @Override
    public Page<CouponVO> pageByStore(String storeId, PageVO page) {
        throw new ServiceException();
    }
}
