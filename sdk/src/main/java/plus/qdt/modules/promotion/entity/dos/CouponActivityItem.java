package plus.qdt.modules.promotion.entity.dos;

import plus.qdt.mybatis.model.BaseStandardEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 优惠券活动实体类
 *
 * <AUTHOR>
 * @since 2020-03-19 10:44 上午
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("li_coupon_activity_item")
@Schema(title = "优惠券活动-优惠券关联实体类")
public class CouponActivityItem extends BaseStandardEntity {

    @Schema(title = "优惠券活动ID")
    private String activityId;

    @Schema(title = "优惠券ID")
    private String couponId;

    @Schema(title = "优惠券数量")
    private Integer num;


}