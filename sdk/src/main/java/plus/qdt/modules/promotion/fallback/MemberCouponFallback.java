package plus.qdt.modules.promotion.fallback;

import plus.qdt.common.exception.ServiceException;
import plus.qdt.modules.promotion.client.MemberCouponClient;

import java.util.List;

/**
 * @author: ftyy
 * @date: 2022-01-13 17:45
 * @description: 会员优惠券Fallback
 */
public class MemberCouponFallback implements MemberCouponClient {


    @Override
    public void receiveCoupon(String couponId, String memberId, String memberName) {
        throw new ServiceException();
    }

    @Override
    public void used(String memberId, List<String> ids) {
        throw new ServiceException();
    }

    /**
     * 作废无效的会员优惠券
     *
     * @return 是否操作成功
     */
    @Override
    public boolean expireInvalidMemberCoupon() {
        throw new ServiceException();
    }

    @Override
    public boolean expireInvalidMemberCoupon(String memberId, String couponId) {
        throw new ServiceException();
    }

    /**
     * 删除作废/过期的会员优惠券
     *
     * @param expirationDay 过期常量，过期后或者使用后一定时间内，删除无效的优惠券，物理删除
     * @return 是否操作成功
     */
    @Override
    public boolean removeInvalidMemberCoupon(int expirationDay) {
        throw new ServiceException();
    }

    @Override
    public void cleanMemberCouponSign() {
        throw new ServiceException();
    }

    /**
     * 恢复会员优惠券
     *
     * @param memberCouponIds 会员优惠券id列表
     * @return 是否恢复成功
     */
    @Override
    public boolean recoveryMemberCoupon(List<String> memberCouponIds) {
        throw new ServiceException();
    }

    @Override
    public Long getMemberCouponCount(String memberId, String couponId) {
        throw new ServiceException();
    }
}
