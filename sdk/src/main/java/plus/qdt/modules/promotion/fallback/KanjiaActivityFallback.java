package plus.qdt.modules.promotion.fallback;

import plus.qdt.common.exception.ServiceException;
import plus.qdt.modules.promotion.client.KanjiaActivityClient;
import plus.qdt.modules.promotion.entity.dos.KanjiaActivity;
import plus.qdt.modules.promotion.entity.dto.search.KanjiaActivitySearchParams;
import org.springframework.stereotype.Component;

/**
 * @author: ftyy
 * @date: 2022-01-13 18:50
 * @description: 描述
 */
public class KanjiaActivityFallback implements KanjiaActivityClient {

    /**
     * 结束砍价活动
     *
     * @param kanjiaId 砍价活动id
     * @return 是否更新成功
     */
    @Override
    public boolean endKanjiaActivity(String kanjiaId) {
        throw new ServiceException();
    }

    @Override
    public KanjiaActivity getById(String promotionId) {
        throw new ServiceException();
    }

    @Override
    public KanjiaActivity getKanjiaActivity(KanjiaActivitySearchParams kanJiaActivitySearchParams) {
        throw new ServiceException();
    }
}
