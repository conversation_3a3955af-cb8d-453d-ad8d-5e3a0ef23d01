package plus.qdt.modules.promotion.entity.dos;

import plus.qdt.mybatis.model.BaseStandardEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 砍价活动商品实体类
 *
 * <AUTHOR>
 * @date 2020-7-1 10:44 上午
 */
@Data
@TableName("li_kanjia_activity_log")
@Schema(title = "砍价活动日志对象")
public class KanjiaActivityLog extends BaseStandardEntity {


    private static final long serialVersionUID = 3977352717995562783L;

    @Schema(title = "砍价活动参与记录id")
    private String kanjiaActivityId;

    @Schema(title = "砍价会员id")
    private String kanjiaMemberId;

    @Schema(title = "砍价会员名称")
    private String kanjiaMemberName;

    @Schema(title = "砍价会员头像")
    private String kanjiaMemberFace;

    @Schema(title = "砍价金额")
    private Double kanjiaPrice;

    @Schema(title = "剩余购买金额")
    private Double surplusPrice;


}