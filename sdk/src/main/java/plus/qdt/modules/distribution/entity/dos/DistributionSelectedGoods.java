package plus.qdt.modules.distribution.entity.dos;

import plus.qdt.mybatis.model.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 分销员已选择分销商品
 *
 * <AUTHOR>
 * @since 2020-03-14 23:04:56
 */
@Data
@Schema(title = "分销商已选择分销商品")
@TableName("li_distribution_selected_goods")
@NoArgsConstructor
public class DistributionSelectedGoods extends BaseEntity {


    @Schema(title = "分销员ID")
    private String distributionId;

    @Schema(title = "分销商品ID")
    private String distributionGoodsId;

    public DistributionSelectedGoods(String distributionId, String distributionGoodsId) {
        this.distributionId = distributionId;
        this.distributionGoodsId = distributionGoodsId;
    }
}
