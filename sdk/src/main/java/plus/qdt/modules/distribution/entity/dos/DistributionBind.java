package plus.qdt.modules.distribution.entity.dos;

import plus.qdt.common.enums.SwitchEnum;
import plus.qdt.mybatis.model.BaseStandardEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 分销员对象
 *
 * <AUTHOR>
 * @since 2020-03-14 23:04:56
 */
@Data
@Schema(title = "分销员关系绑定")
@TableName("li_distribution_bind")
@NoArgsConstructor
public class DistributionBind extends BaseStandardEntity {

    private static final long serialVersionUID = -4878132663540847325L;

    @Schema(title = "会员id")
    private String memberId;
    @Schema(title = "会员昵称")
    private String nickName;
    @Schema(title = "分销员ID")
    private String distributionId;
    @Schema(title = "分销绑定模式（有效期/永久）")
    private String validity;
    @Schema(title = "失效时间")
    private Date validityDay;
    /**
     * @see SwitchEnum
     */
    @Schema(title = "状态")
    private String status;
    @Schema(title = "成交总额")
    private Double orderPrice;
    @Schema(title = "佣金总额")
    private Double rebatePrice;
    @Schema(title = "订单数量")
    private Integer orderNum;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "最后一次登录时间")
    private Date lastLoginDate;

}