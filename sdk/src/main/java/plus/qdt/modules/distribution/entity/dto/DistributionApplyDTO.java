package plus.qdt.modules.distribution.entity.dto;

import plus.qdt.common.utils.ValidateParamsUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 分销员申请DTO
 * <AUTHOR>
 * @since 2021/6/30 11:07 上午
 *
 */
@Data
public class DistributionApplyDTO {

    @NotBlank(message = "姓名不能为空")
    @Schema(title = "会员姓名")
    private String name;

    @NotBlank(message = "身份证号不能为空")
    @Schema(title = "身份证号")
    private String idNumber;

    @Length(min = 1, max = 200, message = "结算银行开户行名称长度为1-200位")
    @NotBlank(message = "结算银行开户行名称不能为空")
    @Schema(title = "结算银行开户行名称")
    private String settlementBankAccountName;

    @Length(min = 1, max = 200, message = "结算银行开户账号长度为1-200位")
    @NotBlank(message = "结算银行开户账号不能为空")
    @Schema(title = "结算银行开户账号")
    private String settlementBankAccountNum;

    @Length(min = 1, max = 200, message = "结算银行开户支行名称长度为1-200位")
    @NotBlank(message = "结算银行开户支行名称不能为空")
    @Schema(title = "结算银行开户支行名称")
    private String settlementBankBranchName;

    public boolean validateParams() {
        if (!ValidateParamsUtil.isValidString(name, 1, 200)) {
            ValidateParamsUtil.throwInvalidParamError("姓名格式不正确");
        }
        if (!ValidateParamsUtil.isValidIdCard(idNumber)) {
            ValidateParamsUtil.throwInvalidParamError("身份证号格式不正确");
        }
        if (!ValidateParamsUtil.isValidString(settlementBankAccountName, 1, 200, false)) {
            ValidateParamsUtil.throwInvalidParamError("结算银行开户行名称格式不正确");
        }
        if (!ValidateParamsUtil.isValidString(settlementBankAccountNum, 1, 200, false)) {
            ValidateParamsUtil.throwInvalidParamError("结算银行开户账号格式不正确");
        }
        if (!ValidateParamsUtil.isValidString(settlementBankBranchName, 1, 200, false)) {
            ValidateParamsUtil.throwInvalidParamError("结算银行开户支行名称格式不正确");
        }
        return true;
    }
}
