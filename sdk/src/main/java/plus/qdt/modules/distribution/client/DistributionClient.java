package plus.qdt.modules.distribution.client;

import plus.qdt.modules.constant.ServiceConstant;
import plus.qdt.modules.distribution.entity.dos.Distribution;
import plus.qdt.modules.distribution.entity.dos.DistributionBind;
import plus.qdt.modules.distribution.entity.dos.DistributionStore;
import plus.qdt.modules.distribution.entity.vos.DistributionVO;
import plus.qdt.modules.distribution.fallback.DistributionFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 商品服务分销商品client
 *
 * <AUTHOR>
 * @version v1.0 2021-11-08 09:52
 */
@FeignClient(name = ServiceConstant.DISTRIBUTION_SERVICE, contextId = "distribution", fallback = DistributionFallback.class)
public interface DistributionClient {

    @GetMapping("/feign/distribution/updateDistributionAmount")
    void updateDistributionAmount();


    @GetMapping("/feign/distribution/getDistributionBind/{memberId}")
    DistributionBind getDistributionBind(@PathVariable String memberId);

    /**
     * 获取上级分销员绑定信息
     *
     * @param memberId 会员ID
     * @return 分销员绑定信息
     */
    @GetMapping("/feign/distribution/getUpDistributionBind/{memberId}")
    List<DistributionVO> getUpDistributionBind(@PathVariable String memberId);

    @GetMapping("/feign/distribution/getDistributionById/{distributionId}")
    Distribution getDistributionById(@PathVariable String distributionId);

    /**
     * 根据店铺ID获取分销设置
     *
     * @param storeId
     * @return 店铺分销设置
     */
    @GetMapping("/feign/distribution/getDistributionStore")
    DistributionStore getByStoreId(@RequestParam String storeId);
}
