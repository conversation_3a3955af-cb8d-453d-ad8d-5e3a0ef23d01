package plus.qdt.modules.distribution.fallback;

import plus.qdt.common.exception.ServiceException;
import plus.qdt.modules.distribution.client.DistributionSelectedGoodsClient;
import org.springframework.stereotype.Component;

/**
 * @author: ftyy
 * @date: 2022-01-14 10:34
 * @description: 分销商已选择分销商品Fallback
 */
public class DistributionSelectedGoodsFallback implements DistributionSelectedGoodsClient {
    @Override
    public boolean add(String distributionGoodsId) {
        throw new ServiceException();
    }

    @Override
    public boolean delete(String distributionGoodsId) {
        throw new ServiceException();
    }

    @Override
    public boolean deleteByDistributionGoodsId(String distributionGoodsId) {
        throw new ServiceException();
    }
}
