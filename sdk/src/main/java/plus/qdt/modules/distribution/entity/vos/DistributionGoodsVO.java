package plus.qdt.modules.distribution.entity.vos;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 分销商品信息
 *
 * <AUTHOR>
 * @since 2020-03-26 09:04:53
 */
@Data
public class DistributionGoodsVO {

    @Schema(title = "分销商品ID")
    private String id;

    @Schema(title = "商品名称")
    private String goodsName;

    @Schema(title = "规格")
    private String specs;

    @Schema(title = "库存")
    private Integer quantity;

    @Schema(title = "商品图片")
    private String thumbnail;

    @Schema(title = "商品价格")
    private Double price;

    @Schema(title = "商品编号")
    private String sn;

    @Schema(title = "商品ID")
    private String goodsId;

    @Schema(title = "规格ID")
    private String skuId;

    @Schema(title = "店铺名称")
    private String storeName;

    @Schema(title = "佣金金额")
    private Double commission;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "添加时间")
    private Date createTime;

}
