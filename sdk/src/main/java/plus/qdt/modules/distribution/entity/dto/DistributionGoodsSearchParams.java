package plus.qdt.modules.distribution.entity.dto;

import cn.hutool.core.text.CharSequenceUtil;
import plus.qdt.common.security.context.UserContext;
import plus.qdt.common.vo.PageVO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Objects;

/**
 * 分销员商品查询条件
 *
 * <AUTHOR>
 * @since 2020-03-14 23:04:56
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DistributionGoodsSearchParams extends PageVO {

    @Schema(title = "商品ID")
    private String goodsId;

    @Schema(title = "商品名称")
    private String goodsName;

    @Schema(title = "是否已选择")
    private boolean isChecked;

    public <T> QueryWrapper<T> queryWrapper() {
        QueryWrapper<T> queryWrapper = this.distributionQueryWrapper();
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(goodsId), "goods_id", goodsId);
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(goodsName), "goods_name", goodsId);
        return queryWrapper;
    }

    public <T> QueryWrapper<T> storeQueryWrapper() {
        QueryWrapper<T> queryWrapper = this.distributionQueryWrapper();
        queryWrapper.eq("dg.store_id", Objects.requireNonNull(UserContext.getCurrentUser()).getExtendId());
        return queryWrapper;
    }

    public <T> QueryWrapper<T> distributionQueryWrapper() {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        queryWrapper.like(CharSequenceUtil.isNotEmpty(goodsName), "dg.goods_name", goodsName);
        return queryWrapper;
    }

}
