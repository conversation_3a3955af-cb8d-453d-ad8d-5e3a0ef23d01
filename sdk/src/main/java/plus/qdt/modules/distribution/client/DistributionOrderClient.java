package plus.qdt.modules.distribution.client;

import plus.qdt.modules.constant.ServiceConstant;
import plus.qdt.modules.distribution.entity.dos.DistributionOrder;
import plus.qdt.modules.distribution.fallback.DistributionOrderFallBack;
import plus.qdt.modules.order.aftersale.entity.dos.AfterSale;
import plus.qdt.modules.order.order.entity.dos.OrderItemFlow;
import plus.qdt.modules.store.entity.dos.Store;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @author: ftyy
 * @date: 2022-01-13 17:02
 * @description: 分销订单Client
 */
@FeignClient(name = ServiceConstant.DISTRIBUTION_SERVICE, contextId = "distribution-order-1", fallback = DistributionOrderFallBack.class)
public interface DistributionOrderClient {


    @PutMapping("/feign/distribution-order/calculationDistribution")
    void calculationDistribution(@RequestParam String orderSn);

    @PutMapping("/feign/distribution-order/cancelOrder")
    void cancelOrder(@RequestParam String orderSn);

    @PutMapping("/feign/distribution-order/refundOrder")
    void refundOrder(@RequestBody AfterSale afterSale);

    @PutMapping("/feign/distribution-order/updateDistributionOrderStatus")
    void  updateDistributionOrderStatus(@RequestBody OrderItemFlow orderItemFlow);

    @PutMapping("/feign/distribution-order/updateDistributionOrderByOrderSn")
    void updateDistributionOrderByOrderSn(@RequestParam String orderSn);

    /**
     * 修改分销订单店铺名称
     *
     * @param store 店铺信息
     */
    @PutMapping("/feign/distribution-order/updateStoreName")
    void updateStoreName(@RequestBody Store store);

    /**
     * 获取待结算的分销订单
     *
     * @param storeId 店铺id
     * @return
     */
    @GetMapping("/feign/distribution-order/getPendingDistributionOrderList")
    List<DistributionOrder> getPendingDistributionOrderListByStoreId(@RequestParam String storeId);

    /**
     * 结算分销订单
     *
     * @param distributionOrder
     */
    @PutMapping("/feign/distribution-order/settlementDistributionOrder")
    void settlementDistributionOrder(@RequestBody DistributionOrder distributionOrder);
}
