package plus.qdt.modules.wechat.entity.params;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: ftyy
 * @date: 2022-01-19 15:48
 * @description: 直播间
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WechatParams {

    @Schema(title = "商品id")
    private Integer goodsId;

    @Schema(title = "直播间id")
    private Integer roomId;
}
