package plus.qdt.modules.wechat.entity.dos;

import plus.qdt.mybatis.model.BaseStandardEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 微信消息
 *
 * <AUTHOR>
 * @version v4.0
 * @since 2020/12/10 17:02
 */
@Data
@TableName("li_wechat_message")
@Schema(title = "微信消息")
@EqualsAndHashCode(callSuper = true)
public class WechatMessage extends BaseStandardEntity {

    private static final long serialVersionUID = -9157586585885836755L;

    @Schema(title = "模版名称")
    private String name;

    @Schema(title = "微信模版码")
    private String code;

    /**
     * map 格式，key为我们的模版消息的key，value为微信模版消息的key
     * 例如：昵称 ---- NICKNAME:thing5
     */
    @Schema(title = "关键字")
    private String keywords;

    @Schema(title = "是否开启")
    private Boolean enable = true;

    @Schema(title = "订单状态")
    private String orderStatus;

    @Schema(title = "模版头部信息")
    private String first;

    @Schema(title = "模版备注（位于最下方）")
    private String remark;


}