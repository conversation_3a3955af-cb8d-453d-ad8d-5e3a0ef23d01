package plus.qdt.modules.permission.client;


import plus.qdt.modules.constant.ServiceConstant;
import plus.qdt.modules.permission.entity.vo.UserMenuVO;
import plus.qdt.modules.permission.fallback.PermissionFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 权限客户端
 *
 * <AUTHOR> @ gmail.com)
 * @version v4.0
 * @Description:
 * @since 2022/8/31 14:51
 */
@FeignClient(name = ServiceConstant.USER_SERVICE, contextId = "permission", fallback = PermissionFallback.class)
public interface PermissionClient {


    @GetMapping("/feign/permission/get")
    List<UserMenuVO> getUserPermission(@RequestParam String userId);

}
