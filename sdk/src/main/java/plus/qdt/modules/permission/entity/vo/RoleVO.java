package plus.qdt.modules.permission.entity.vo;

import plus.qdt.modules.permission.entity.dos.Role;
import plus.qdt.modules.permission.entity.dos.RoleMenu;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * RoleVO
 *
 * <AUTHOR>
 * @since 2020-11-22 17:42
 */
@Data
public class RoleVO extends Role {

    private static final long serialVersionUID = 8625345346785692513L;

    @Schema(title = "拥有权限")
    private List<RoleMenu> roleMenus;
}
