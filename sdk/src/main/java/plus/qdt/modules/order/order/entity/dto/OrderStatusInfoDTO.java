package plus.qdt.modules.order.order.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 订单状态信息DTO
 * 用于流式处理统计订单各状态数量
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "订单状态信息")
public class OrderStatusInfoDTO {

    @Schema(description = "订单编号")
    private String orderSn;

    @Schema(description = "订单状态")
    private String orderStatus;

    @Schema(description = "支付状态")
    private String payStatus;

    @Schema(description = "评价状态")
    private String commentStatus;

    @Schema(description = "售后状态")
    private String afterSaleStatus;

    @Schema(description = "商品分类ID")
    private String categoryId;


}
