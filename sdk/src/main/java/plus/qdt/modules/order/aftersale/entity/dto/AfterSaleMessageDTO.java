package plus.qdt.modules.order.aftersale.entity.dto;

import plus.qdt.modules.order.aftersale.entity.dos.AfterSale;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/6/2114:46
 */
@Data
public class AfterSaleMessageDTO extends AfterSale {
    @Schema(title = "回退标识")
    Boolean returnFlag;
    @Schema(title = "回退数量")
    Integer returnStock;

    public boolean getRenturnFlag(){
        if(returnFlag == null){
            return false;
        }
        return returnFlag;
    }
}
