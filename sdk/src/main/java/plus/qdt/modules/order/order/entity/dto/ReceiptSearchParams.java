package plus.qdt.modules.order.order.entity.dto;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 发票搜索参数
 *
 * <AUTHOR>
 * @since 2021/1/12
 **/
@Data
public class ReceiptSearchParams {

    @Schema(title = "发票抬头")
    private String receiptTitle;

    @Schema(title = "纳税人识别号")
    private String taxpayerId;

    @Schema(title = "会员ID")
    private String memberId;

    @Schema(title = "会员名称")
    private String memberName;

    @Schema(title = "店铺名称")
    private String storeName;

    @Schema(title = "商家ID")
    private String storeId;

    @Schema(title = "订单号")
    private String orderSn;

    @Schema(title = "发票状态")
    private String receiptStatus;

    public <T> QueryWrapper<T> wrapper() {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        if (CharSequenceUtil.isNotEmpty(receiptTitle)) {
            queryWrapper.eq("r.receipt_title", receiptTitle);
        }
        if (CharSequenceUtil.isNotEmpty(taxpayerId)) {
            queryWrapper.eq("r.taxpayer_id", taxpayerId);
        }
        if (CharSequenceUtil.isNotEmpty(memberId)) {
            queryWrapper.eq("r.member_id", memberId);
        }
        if (CharSequenceUtil.isNotEmpty(storeName)) {
            queryWrapper.eq("r.store_name", storeName);
        }
        if (CharSequenceUtil.isNotEmpty(storeId)) {
            queryWrapper.eq("r.store_id", storeId);
        }
        if (CharSequenceUtil.isNotEmpty(memberName)) {
            queryWrapper.eq("r.member_name", memberName);
        }
        if (CharSequenceUtil.isNotEmpty(receiptStatus)) {
            queryWrapper.eq("r.receipt_status", receiptStatus);
        }
        if (CharSequenceUtil.isNotEmpty(orderSn)) {
            queryWrapper.eq("r.order_sn", orderSn);
        }
        queryWrapper.eq("r.delete_flag", false);
        return queryWrapper;
    }

}
