package plus.qdt.modules.order.order.entity.vo;

import cn.hutool.core.text.CharSequenceUtil;
import plus.qdt.common.enums.ClientTypeEnum;
import plus.qdt.modules.order.order.entity.enums.OrderItemAfterSaleStatusEnum;
import plus.qdt.modules.order.order.entity.enums.OrderPromotionTypeEnum;
import plus.qdt.modules.order.order.entity.enums.OrderStatusEnum;
import plus.qdt.modules.order.order.entity.enums.PayStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 订单简略信息
 * 用于订单列表查看
 *
 * <AUTHOR>
 * @since 2020-08-17 20:28
 */
@Data
public class OrderSimpleVO {

    @Schema(title = "sn")
    private String sn;


    @Schema(title = "sn")
    private String tradeSn;

    @Schema(title = "总价格")
    private Double flowPrice;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "创建时间")
    private Date createTime;

    /**
     * @see OrderStatusEnum
     */
    @Schema(title = "订单状态")
    private String orderStatus;

    /**
     * @see PayStatusEnum
     */
    @Schema(title = "付款状态")
    private String payStatus;

    @Schema(title = "支付方式")
    private String paymentMethod;

    @Schema(title = "支付时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date paymentTime;

    @Schema(title = "用户名")
    private String nickname;

    @Schema(title = "店铺名称")
    private String storeName;

    @Schema(title = "店铺ID")
    private String storeId;

    /**
     * @see ClientTypeEnum
     */
    @Schema(title = "订单来源")
    private String clientType;

    /**
     * 子订单信息
     */
    private List<OrderItemVO> orderItems;

    @Schema(hidden = true, title = "item goods_id")
    @Setter
    private String groupGoodsId;

    @Schema(hidden = true, title = "item sku id")
    @Setter
    private String groupSkuId;

    @Schema(hidden = true, title = "item 数量")
    @Setter
    private String groupNum;

    @Schema(hidden = true, title = "item 图片")
    @Setter
    private String groupImages;

    @Schema(hidden = true, title = "item 名字")
    @Setter
    private String groupName;

    @Schema(hidden = true, title = "item 规格")
    @Setter
    private String groupSpecs;

    @Schema(hidden = true, title = "item 编号")
    @Setter
    private String groupOrderItemsSn;

    @Schema(hidden = true, title = "item 商品价格")
    @Setter
    private String groupGoodsPrice;
    /**
     * @see plus.qdt.modules.order.order.entity.enums.OrderItemAfterSaleStatusEnum
     */
    @Schema(hidden = true, title = "item 售后状态", allowableValues = "NOT_APPLIED(未申请),ALREADY_APPLIED(已申请),EXPIRED(已失效不允许申请售后)")
    @Setter
    private String groupAfterSaleStatus;

    /**
     * @see plus.qdt.modules.order.order.entity.enums.OrderComplaintStatusEnum
     */
    @Schema(hidden = true, title = "item 投诉状态")
    @Setter
    private String groupComplainStatus;

    /**
     * @see plus.qdt.modules.order.order.entity.enums.CommentStatusEnum
     */
    @Schema(hidden = true, title = "item 评价状态")
    @Setter
    private String groupCommentStatus;


    /**
     * @see plus.qdt.modules.order.order.entity.enums.OrderTypeEnum
     */
    @Schema(title = "订单类型")
    private String orderType;

    /**
     * @see plus.qdt.modules.order.order.entity.enums.DeliverStatusEnum
     */
    @Schema(title = "货运状态")
    private String deliverStatus;

    /**
     * @see plus.qdt.modules.order.order.entity.enums.OrderPromotionTypeEnum
     */
    @Schema(title = "订单促销类型")
    private String orderPromotionType;

    @Schema(title = "卖家订单备注")
    private String sellerRemark;

    public List<OrderItemVO> getOrderItems() {
        if (CharSequenceUtil.isEmpty(groupGoodsId)) {
            return new ArrayList<>();
        }
        List<OrderItemVO> orderItemVOS = new ArrayList<>();


        String[] goodsId = groupGoodsId.split(",");

        for (int i = 0; i < goodsId.length; i++) {
            orderItemVOS.add(this.getOrderItemVO(i));
        }
        return orderItemVOS;

    }

    private OrderItemVO getOrderItemVO(int i) {
        OrderItemVO orderItemVO = new OrderItemVO();
        orderItemVO.setGoodsId(groupGoodsId.split(",")[i]);
        if (CharSequenceUtil.isNotEmpty(groupOrderItemsSn)) {
            orderItemVO.setSn(groupOrderItemsSn.split(",")[i]);
        }
        if (CharSequenceUtil.isNotEmpty(groupSkuId)) {
            orderItemVO.setSkuId(groupSkuId.split(",")[i]);
        }
        if (CharSequenceUtil.isNotEmpty(groupName)) {
            orderItemVO.setName(groupName.split(",")[i]);
        }
        if (CharSequenceUtil.isNotEmpty(groupNum) && groupNum.split(",").length == groupGoodsId.split(",").length) {
            orderItemVO.setNum(groupNum.split(",")[i]);
        }
        if (CharSequenceUtil.isNotEmpty(groupImages) && groupImages.split(",").length == groupGoodsId.split(",").length) {
            orderItemVO.setImage(groupImages.split(",")[i]);
        }
        if (CharSequenceUtil.isNotEmpty(groupSpecs) && groupSpecs.split(",").length == groupGoodsId.split(",").length) {
            orderItemVO.setSpecs(groupSpecs.split(",")[i]);
        }
        if (CharSequenceUtil.isNotEmpty(groupAfterSaleStatus) && groupAfterSaleStatus.split(",").length == groupGoodsId.split(",").length) {
            if (!OrderPromotionTypeEnum.isCanAfterSale(this.orderPromotionType)) {
                orderItemVO.setAfterSaleStatus(OrderItemAfterSaleStatusEnum.EXPIRED.name());
            } else {
                orderItemVO.setAfterSaleStatus(groupAfterSaleStatus.split(",")[i]);
            }
        }
        if (CharSequenceUtil.isNotEmpty(groupComplainStatus) && groupComplainStatus.split(",").length == groupGoodsId.split(",").length) {
            orderItemVO.setComplainStatus(groupComplainStatus.split(",")[i]);
        }
        if (CharSequenceUtil.isNotEmpty(groupCommentStatus) && groupCommentStatus.split(",").length == groupGoodsId.split(",").length) {
            orderItemVO.setCommentStatus(groupCommentStatus.split(",")[i]);
        }
        if (CharSequenceUtil.isNotEmpty(groupGoodsPrice) && groupGoodsPrice.split(",").length == groupGoodsId.split(",").length) {
            orderItemVO.setGoodsPrice(Double.parseDouble(groupGoodsPrice.split(",")[i]));
        }
        return orderItemVO;
    }

    /**
     * 初始化自身状态
     */
    public AllowOperation getAllowOperationVO() {
        //设置订单的可操作状态
        return new AllowOperation(this);
    }

    public String getGroupAfterSaleStatus() {
        // 不可售后的订单类型集合
        if (!OrderPromotionTypeEnum.isCanAfterSale(this.orderPromotionType)) {
            return OrderItemAfterSaleStatusEnum.EXPIRED.name();
        }
        // 已完成订单不可售后
        if (OrderStatusEnum.COMPLETED.name().equals(this.orderStatus)) {
            return OrderItemAfterSaleStatusEnum.EXPIRED.name();
        }
        return groupAfterSaleStatus;
    }

}
