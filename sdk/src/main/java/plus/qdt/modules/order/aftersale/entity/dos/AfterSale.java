package plus.qdt.modules.order.aftersale.entity.dos;

import plus.qdt.modules.order.order.entity.enums.OrderTypeEnum;
import plus.qdt.mybatis.model.BaseStandardEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.util.Date;

/**
 * 售后
 *
 * <AUTHOR>
 * @since 2020/11/17 7:30 下午
 */
@Data
@TableName("li_after_sale")
@EqualsAndHashCode(callSuper = true)
@Schema(title = "售后")
public class AfterSale extends BaseStandardEntity {

    @Serial
    private static final long serialVersionUID = -5339221840646353416L;

    //基础信息
    @Schema(title = "售后服务单号")
    private String sn;

    @Schema(title = "订单编号")
    private String orderSn;

    @Schema(title = "订单货物编号")
    private String orderItemSn;

    @Schema(title = "交易编号")
    private String tradeSn;

    @Schema(title = "会员ID")
    private String memberId;

    @Schema(title = "会员名称")
    private String memberName;

    @Schema(title = "商家ID")
    private String storeId;

    @Schema(title = "商家名称")
    private String storeName;

    //商品信息

    @Schema(title = "商品ID")
    private String goodsId;
    @Schema(title = "货品ID")
    private String skuId;
    @Schema(title = "申请数量")
    private Integer num;
    @Schema(title = "商品图片")
    private String goodsImage;
    @Schema(title = "商品名称")
    private String goodsName;

    @Schema(title = "规格json")
    private String specs;


    //交涉信息

    @Schema(title = "申请原因")
    private String reason;

    @Schema(title = "问题描述")
    private String problemDesc;

    @Schema(title = "评价图片")
    private String afterSaleImage;

    /**
     * @see plus.qdt.modules.order.trade.entity.enums.AfterSaleTypeEnum
     */
    @Schema(title = "售后类型", allowableValues = "RETURN_GOODS,RETURN_MONEY")
    private String serviceType;

    /**
     * @see plus.qdt.modules.order.trade.entity.enums.AfterSaleStatusEnum
     */
    @Schema(title = "售后单状态", allowableValues = "APPLY,PASS,REFUSE,BUYER_RETURN,SELLER_RE_DELIVERY,BUYER_CONFIRM,SELLER_CONFIRM,COMPLETE")
    private String serviceStatus;

    //退款信息

    /**
     * @see plus.qdt.modules.order.trade.entity.enums.AfterSaleRefundWayEnum
     */
    @Schema(title = "退款方式", allowableValues = "ORIGINAL,OFFLINE")
    private String refundWay;

    @Schema(title = "账号类型", allowableValues = "ALIPAY,WECHATPAY,BANKTRANSFER")
    private String accountType;

    @Schema(title = "银行账户")
    private String bankAccountNumber;

    @Schema(title = "银行开户名")
    private String bankAccountName;

    @Schema(title = "银行开户行")
    private String bankDepositName;

    @Schema(title = "商家备注")
    private String auditRemark;

    @Schema(title = "订单支付方式返回的交易号")
    private String payOrderNo;

    @Schema(title = "申请退款金额")
    private Double applyRefundPrice;

    @Schema(title = "实际退款金额")
    private Double actualRefundPrice;

    @Schema(title = "退还积分")
    private Integer refundPoint;

    @Schema(title = "退款时间")
    private Date refundTime;

    /**
     * 买家物流信息
     */
    @Schema(title = "发货单号")
    private String mLogisticsNo;

    @Schema(title = "物流公司CODE")
    private String mLogisticsCode;

    @Schema(title = "物流公司名称")
    private String mLogisticsName;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(title = "买家发货时间")
    private Date mDeliverTime;

    /**
     * @see OrderTypeEnum
     */
    @Schema(title = "售后订单类型")
    private String serviceOrderType;

    @Schema(title = "供应商ID")
    private String supplierId;

    @Schema(title = "代理订单")
    private Boolean isProxy = false;

}