package plus.qdt.modules.page.entity.enums;

/**
 * 楼层装修枚举
 *
 * <AUTHOR>
 * @since 2020/12/7 10:50
 */
public enum PageTypeEnum {

    /**
     * 首页
     */
    INDEX,

    /**
     * 专题页面
     */
    SPECIAL,
    /**
     * 开屏弹出广告
     */
    OPEN_SCREEN_PAGE,
    /**
     * 发现
     */
    DISCOVER,
    /**
     * 开屏动画
     */
    OPEN_SCREEN_ANIMATION;

    public String value() {
        return this.name();
    }

}
