package plus.qdt.modules.page.entity.dos;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.http.HtmlUtil;
import plus.qdt.common.security.enums.SceneEnums;
import plus.qdt.modules.page.entity.enums.ArticleEnum;
import plus.qdt.mybatis.model.BaseStandardEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 文章DO
 *
 * <AUTHOR>
 * @since 2020/12/10 17:42
 */
@Data
@TableName("li_article")
@Schema(title = "文章")
public class Article extends BaseStandardEntity {

    private static final long serialVersionUID = 1L;

    @Schema(title = "文章标题")
    @NotEmpty(message = "文章标题不能为空")
    @Length(max = 30, message = "文章标题不能超过30个字符")
    private String title;

    @Schema(title = "分类id")
    @NotNull(message = "文章分类不能为空")
    private String categoryId;

    @Schema(title = "文章排序")
    private Integer sort;

    @Schema(title = "文章内容")
    @NotEmpty(message = "文章内容不能为空")
    private String content;

    @Schema(title = "状态")
    private Boolean openStatus;
    /**
     * @see ArticleEnum
     */
    @Schema(title = "类型")
    private String type;

    /**
     * @see SceneEnums
     */
    @Schema(title = "场景")
    private String scene;

    public String getContent() {
        if (CharSequenceUtil.isNotEmpty(content)) {
            return HtmlUtil.unescape(content);
        }
        return content;
    }

}