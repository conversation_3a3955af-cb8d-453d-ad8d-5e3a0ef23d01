package plus.qdt.modules.member.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 会员评价DTO
 *
 * <AUTHOR>
 * @since 2020/11/29 11:13 下午
 */
@Data
public class MemberEvaluationDTO {

    @Schema(title = "子订单编号")
    @NotEmpty(message = "订单异常")
    private String orderItemSn;

    @Schema(title = "商品ID")
    @NotEmpty(message = "订单商品异常不能为空")
    private String goodsId;

    @Schema(title = "规格ID")
    @NotEmpty(message = "订单商品不能为空")
    private String skuId;

    @Schema(title = "好中差评价")
    @NotEmpty(message = "请评价")
    private String grade;

    @Schema(title = "评论内容")
    @NotEmpty(message = "评论内容不能为空")
    @Length(max = 500, message = "评论内容不能超过500字符")
    private String content;

    @Schema(title = "评论图片")
    private String images;

    @Schema(title = "物流评分")
    private Integer deliveryScore;

    @Schema(title = "服务评分")
    private Integer serviceScore;

    @Schema(title = "描述评分")
    private Integer descriptionScore;


}
