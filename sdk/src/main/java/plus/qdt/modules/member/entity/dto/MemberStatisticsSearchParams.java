package plus.qdt.modules.member.entity.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2022/6/28
 **/
@Data
@NoArgsConstructor
public class MemberStatisticsSearchParams {

    private Date endTime;
    private Date startTime;


    public MemberStatisticsSearchParams(Date endTime, Date startTime) {
        this.endTime = endTime;
        this.startTime = startTime;
    }
}
