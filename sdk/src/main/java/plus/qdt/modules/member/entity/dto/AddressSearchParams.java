package plus.qdt.modules.member.entity.dto;

import cn.hutool.core.text.CharSequenceUtil;
import plus.qdt.common.security.context.UserContext;
import plus.qdt.common.security.enums.SceneEnums;
import plus.qdt.modules.member.entity.dos.UserAddress;
import plus.qdt.modules.member.entity.enums.AddressTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Objects;

/**
 * 地址检索参数
 *
 * <AUTHOR> @ gmail.com)
 * @version v4.0
 * @Description:
 * @since 2023/4/21 14:58
 */
@Data
public class AddressSearchParams {

    @Schema(title = "id")
    private String id;

    @Schema(title = "地址类型")
    private AddressTypeEnum type = AddressTypeEnum.RECEIVE;

    @Schema(title = "场景")
    private SceneEnums scene = SceneEnums.MEMBER;

    @Schema(title = "扩展id")
    private String extendId;

    public QueryWrapper<UserAddress> queryWrapper() {
        QueryWrapper<UserAddress> queryWrapper = new QueryWrapper<>();
        switch (Objects.requireNonNull(UserContext.getCurrentUser()).getScene()) {
            case SUPPLIER, STORE, MEMBER -> {
                this.scene = UserContext.getCurrentUser().getScene();
                this.extendId = UserContext.getCurrentUser().getExtendId();
            }
            case MANAGER -> this.scene = null;
            default -> {
            }
        }

        if (type != null) {
            queryWrapper.eq("type", this.type.name());
        }
        if (scene != null) {
            queryWrapper.eq("scene", this.scene.name());
        }
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(id), "id", this.id);
        queryWrapper.eq(extendId != null, "extend_id", this.extendId);
        return queryWrapper;
    }

    public QueryWrapper<UserAddress> queryWrapperNoAuth() {
        QueryWrapper<UserAddress> queryWrapper = new QueryWrapper<>();

        queryWrapper.eq(CharSequenceUtil.isNotEmpty(id), "id", this.id);
        if (type != null) {
            queryWrapper.eq("type", this.type.name());
        }
        if (scene != null) {
            queryWrapper.eq("scene", this.scene.name());
        }
        queryWrapper.eq(extendId != null, "extend_id", this.extendId);
        return queryWrapper;
    }

}
