package plus.qdt.modules.member.entity.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.format.annotation.DateTimeFormat;
import plus.qdt.common.enums.ClientTypeEnum;
import plus.qdt.common.security.enums.SceneEnums;
import plus.qdt.common.utils.BeanUtil;
import plus.qdt.common.utils.CommonUtil;
import plus.qdt.modules.member.entity.dto.UserInfoDTO;
import plus.qdt.modules.member.entity.enums.PromotionGrade;
import plus.qdt.modules.permission.entity.dto.UserAdminDTO;
import plus.qdt.mybatis.model.BaseSceneEntity;

import java.io.Serial;
import java.util.Date;
import java.util.UUID;

/**
 * 用户
 *
 * <AUTHOR> @ gmail.com)
 * @version v4.0
 * @since 2022/8/23 19:26
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("li_user")
@Schema(title = "用户")
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class User extends BaseSceneEntity {

    @Serial
    private static final long serialVersionUID = -7654006987374239274L;


    @Schema(title = "用户用户名")
    private String username;

    @NotEmpty(message = "手机号码不能为空")
    @Schema(title = "手机号码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String mobile;

    @Schema(title = "用户密码")
    private String password;

    @Schema(title = "昵称")
    private String nickName;

    @Min(message = "用户性别参数错误", value = 0)
    @Schema(title = "用户性别,1为男，0为女,-1 未知")
    private Integer sex;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(title = "用户生日")
    private Date birthday;

    @Schema(title = "头像")
    private String face;

    @Schema(title = "启用状态")
    private Boolean enable;

    @Schema(title = "是否是超级管理员 超级管理员/普通管理员")
    private Boolean isSuper = false;

    /**
     * @see ClientTypeEnum
     */
    @Schema(title = "客户端")
    private String clientEnum;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "最后一次登录时间")
    private Date lastLoginDate;


    @Schema(title = "所属部门id")
    private String departmentId;

    @Schema(title = "角色id集合")
    private String roleIds;

    @Schema(title = "场景昵称")
    private String extendName;

    @Schema(title = "是否是店主", hidden = true)
    private Boolean shopkeeper = false;

    @Schema(title = "标签id集合")
    private String labelIds;

    @Schema(title = "推广码")
    private String promotionCode;

    @Schema(title = "是否实名认证")
    private Boolean realAuthentication;

    @Schema(title = "身份证号")
    private String idCard;

    @Schema(title = "身份证头像照片")
    private String idCardFront;

    @Schema(title = "身份证国徽照片")
    private String idCardBack;

    @Schema(title = "家庭住址")
    private String  address;

    @Schema(title = "真实名字")
    private String realName;

    @Schema(title = "身份证反面有效期")
    private String timeLimit;

    @Schema(title = "是否有会员年卡")
    private Boolean vip;

    @Schema(title = "会员年卡过期时间")
    private Date vipExpire;

    @Schema(title = "行政区划代码", description = "到村级village")
    private String areaCode;

    @Schema(title = "推广等级", description = "默认是普通用户")
    @TableField(exist = false)
    private PromotionGrade promotionGrade = PromotionGrade.ORDINARY;

    public String getExtendName() {
        if (extendName == null) {
            return "";
        }
        return extendName;
    }

    public User(String username, String password, SceneEnums sceneEnums) {

        this.defaultInitialize();
        this.setScene(sceneEnums.value());
        this.username = username;
        this.password = password;
        this.nickName = username;
    }

    public User(String username, String password, String mobile, SceneEnums sceneEnums) {

        this.defaultInitialize();

        this.setScene(sceneEnums.value());
        this.username = username;
        this.password = password;
        this.mobile = mobile;
        this.nickName = username;
    }

    public User(UserInfoDTO userInfoDTO) {
        this.defaultInitialize();
        BeanUtil.copyProperties(userInfoDTO, this);
        this.setAddress(userInfoDTO.getUserAddress());
        this.setScene(userInfoDTO.getScene().value());
        this.setRoleIds(userInfoDTO.getRoleIdsStr());
        if (userInfoDTO.getNickName().equals("临时昵称")) {
            this.setNickName(CommonUtil.randomNickname());
        }
    }

    public User(UserAdminDTO userAdminDTO) {
        this.defaultInitialize();
        BeanUtil.copyProperties(userAdminDTO, this);
        this.setScene(SceneEnums.MANAGER.value());
    }

    /**
     * 初始化默认值
     */
    private void defaultInitialize() {
        this.enable = true;
        this.sex = -1;
        this.birthday = new Date();
    }

    public String getUsername() {
        if (username == null) {
            return UUID.randomUUID().toString();
        }
        return username;
    }

    /**
     * 判断年卡是否有效
     * @return {@link Boolean}
     * <AUTHOR>
     */
    public boolean isRealVip() {
        if (Boolean.TRUE.equals(this.vip) && this.vipExpire != null) {
            return this.vipExpire.after(new Date());
        }
        return true;
    }
}
