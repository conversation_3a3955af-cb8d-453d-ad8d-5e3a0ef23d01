package plus.qdt.modules.member.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 会员评价VO
 *
 * <AUTHOR>
 * @since 2020/11/30 15:00
 */
@Data
public class MemberEvaluationListVO {

    @Schema(title = "评论ID")
    private String id;
    @NotNull
    @Schema(title = "会员头像")
    private String memberProfile;

    @Schema(title = "会员名称")
    private String memberName;

    @Schema(title = "商品名称")
    private String goodsName;

    @Schema(title = "好中差评", allowableValues = "GOOD,NEUTRAL,BAD")
    private String grade;

    @Schema(title = "评价内容")
    private String content;

    @Schema(title = "状态 ", allowableValues = " OPEN 正常 ,CLOSE 关闭")
    private String status;

    @Schema(title = "回复状态")
    private Boolean replyStatus;

    @Schema(title = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Schema(title = "物流评分")
    private Integer deliveryScore;

    @Schema(title = "服务评分")
    private Integer serviceScore;

    @Schema(title = "描述评分")
    private Integer descriptionScore;

    @Schema(title = "商品图片")
    private String goodsImage;

    @Schema(title = "评价图片")
    private String images;

    @Schema(title = "评价回复")
    private String reply;

    @Schema(title = "评价回复图片")
    private String replyImage;

    @Schema(title = "评论是否有图片 true 有 ,false 没有")
    private Boolean haveImage;
}
