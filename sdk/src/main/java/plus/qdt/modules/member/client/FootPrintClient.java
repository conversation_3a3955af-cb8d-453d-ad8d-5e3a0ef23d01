package plus.qdt.modules.member.client;

import plus.qdt.modules.constant.ServiceConstant;
import plus.qdt.modules.member.entity.dos.FootPrint;
import plus.qdt.modules.member.fallback.FootPrintFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: ftyy
 * @date: 2022-01-14 9:57
 * @description: 浏览历史Client
 */
@FeignClient(name = ServiceConstant.USER_SERVICE, contextId = "foot-print", fallback = FootPrintFallback.class)
public interface FootPrintClient {
    /**
     * 保存浏览历史
     *
     * @param footPrint 用户足迹
     * @return 浏览历史
     */
    @PostMapping("/feign/member/foot-print/saveFootprint")
    FootPrint saveFootprint(@RequestBody FootPrint footPrint);

    /**
     * 清空当前会员的足迹
     *
     * @return 处理结果
     */
    @GetMapping("/feign/member/foot-print/clean")
    boolean clean();

    /**
     * 根据ID进行清除会员的历史足迹
     *
     * @param ids 商品ID列表
     * @return 处理结果
     */
    @DeleteMapping("/feign/member/foot-print/deleteByIds")
    boolean deleteByIds(@RequestBody List<String> ids);

    /**
     * 获取当前会员的浏览记录数量
     *
     * @return 当前会员的浏览记录数量
     */
    @GetMapping("/feign/member/foot-print/getFootprintNum")
    long getFootprintNum();
}
