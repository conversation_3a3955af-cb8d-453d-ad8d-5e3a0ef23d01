package plus.qdt.modules.member.entity.dto;

import plus.qdt.mybatis.model.BaseSceneEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 店员dto
 *
 * <AUTHOR>
 * @title: Clerk
 * @projectName qdtplus
 * @date 2021/12/28 7:39 下午
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class ClerkEditDTO extends BaseSceneEntity {

    private static final long serialVersionUID = 1L;


    @Schema(title = "店员id")
    private List<String> ids;

    @Schema(title = "会员密码")
    private String password;

    @Schema(title = "状态")
    private Boolean status;

    @Schema(title = "所属部门id")
    private String departmentId;

    @Schema(title = "是否是超级管理员 超级管理员/普通管理员")
    private Boolean isSuper = false;

    @Schema(title = "角色")
    private List<String> roleIds;



}
