package plus.qdt.modules.member.fallback;

import plus.qdt.common.exception.ServiceException;
import plus.qdt.modules.connect.entity.Connect;
import plus.qdt.modules.member.client.ConnectClient;
import plus.qdt.modules.member.entity.dto.ConnectQueryDTO;

/**
 * @author: ftyy
 * @date: 2022-01-13 15:39
 * @description: 联合登陆 Fallback
 */
public class ConnectFallback implements ConnectClient {
    @Override
    public Connect queryConnect(ConnectQueryDTO connectQueryDTO) {
        throw new ServiceException();
    }

    @Override
    public void deleteConnect(String id) {
        throw new ServiceException();
    }
}
