package plus.qdt.modules.member.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用户积分VO
 *
 * <AUTHOR>
 * @since 2021/2/25 9:52 上午
 */
@Data
public class MemberPointsHistoryVO {

    @Schema(title = "当前用户积分")
    private Long points;

    @Schema(title = "累计获得积分")
    private Long totalPoint;


    public MemberPointsHistoryVO(){
        this.points = 0L;
        this.totalPoint = 0L;
    }
}
