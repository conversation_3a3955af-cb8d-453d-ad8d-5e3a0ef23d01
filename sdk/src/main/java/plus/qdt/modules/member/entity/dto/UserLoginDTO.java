package plus.qdt.modules.member.entity.dto;

import plus.qdt.common.security.enums.SceneEnums;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 用户登录查询参数
 *
 * <AUTHOR>
 * @version v1.0
 * 2022-08-31 15:44
 */
@Data
public class UserLoginDTO {
    /**
     * 用户名/密码/场景
     */
    @NotNull(message = "username cannot be empty")
    private String username;

    @NotNull(message = "password cannot be empty")
    private String password;

    private SceneEnums scene;

    /**
     * 推荐码
     */
    private String promotionCode;
}
