package plus.qdt.modules.member.entity.dos;

import plus.qdt.mybatis.model.BaseSceneEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serial;

/**
 * 用户标签
 *
 * <AUTHOR>
 * @since 2020-02-25 14:10:16
 */
@Data
@TableName("li_user_label")
@Schema(title = "用户标签")
public class UserLabel extends BaseSceneEntity {

    @Serial
    private static final long serialVersionUID = 3454354354354354354L;

    @NotNull
    @Size(max = 20, message = "标签名称长度不能超过20位字符")
    @Schema(title = "标签名称")
    private String labelName;
}