package plus.qdt.modules.member.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import plus.qdt.modules.constant.ServiceConstant;
import plus.qdt.modules.member.entity.enums.PromotionGrade;
import plus.qdt.modules.member.fallback.TeamFallback;

/**
 * 团队相关接口
 *
 * <AUTHOR>
 * @since 2.0
 */
@FeignClient(name = ServiceConstant.USER_SERVICE, contextId = "team", fallback = TeamFallback.class)
public interface TeamClient {

    /**
     * 修改用户数商信息
     * @param userId 用户ID
     * @param giftId 数商ID
     * <AUTHOR>
     */
    @GetMapping("/feign/user/team/update/gift")
    void updateGift(@RequestParam String userId, @RequestParam Integer giftId);

    /**
     * 修改用户身份信息
     * @param userId 用户ID
     * @param grade 数商ID
     * <AUTHOR>
     */
    @GetMapping("/feign/user/team/update/promotion")
    void updatePromotion(@RequestParam String userId, @RequestParam PromotionGrade grade);

}
