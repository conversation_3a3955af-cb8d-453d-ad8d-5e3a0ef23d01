package plus.qdt.modules.auth.serviceimpl;

import cn.hutool.core.text.CharSequenceUtil;
import plus.qdt.cache.Cache;
import plus.qdt.cache.CachePrefix;
import plus.qdt.common.enums.ResultCode;
import plus.qdt.common.exception.ServiceException;
import plus.qdt.common.security.context.UserContext;
import plus.qdt.common.security.token.Token;
import plus.qdt.common.utils.BeanUtil;
import plus.qdt.common.utils.CommonUtil;
import plus.qdt.modules.auth.mapper.ApplicationMapper;
import plus.qdt.modules.auth.service.ApplicationService;
import plus.qdt.modules.member.client.UserClient;
import plus.qdt.modules.member.entity.dos.Application;
import plus.qdt.modules.member.entity.dos.User;
import plus.qdt.modules.member.entity.vo.ApplicationVO;
import plus.qdt.modules.token.TokenGenerate;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * ApplicationServiceImpl
 *
 * <AUTHOR>
 * @version v1.0
 * 2022-08-29 14:35
 */
@Service
@RequiredArgsConstructor
public class ApplicationServiceImpl extends ServiceImpl<ApplicationMapper, Application> implements ApplicationService {


    private final UserClient userClient;

    private final TokenGenerate tokenGenerate;

    private final Cache<String> cache;

    @Override
    public ApplicationVO getByAppid(String appid) {
        LambdaQueryWrapper<Application> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Application::getAppId, appid);
        Application application = this.getOne(lambdaQueryWrapper, false);
        ApplicationVO result = new ApplicationVO();
        BeanUtil.copyProperties(application, result);
        return result;
    }

    @Override
    public String generalCode(String appid) {
        String uuid = CommonUtil.getUUID();
        //默认120秒有效
        cache.put(CachePrefix.OAUTH2_CODE.getPrefix() + uuid, UserContext.getCurrentId(), 120L);
        return uuid;
    }

    @Override
    public Token login(String appid, String code) {
        String userid = cache.get(CachePrefix.OAUTH2_CODE.getPrefix() + code);
        if (CharSequenceUtil.isEmpty(userid)) {
            throw new ServiceException(ResultCode.OAUTH_CODE_EMPTY);
        }

        User user = userClient.getById(userid);
        return tokenGenerate.generateToken(user, false);
    }
}
