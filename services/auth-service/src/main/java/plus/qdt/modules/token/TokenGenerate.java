package plus.qdt.modules.token;

import plus.qdt.common.security.AuthUser;
import plus.qdt.common.security.token.Token;
import plus.qdt.modules.member.entity.dos.User;
import plus.qdt.modules.member.entity.vo.QRCodeLoginSessionVo;
import plus.qdt.modules.member.entity.vo.QRLoginResultVo;

/**
 * AbstractToken
 * 抽象token，定义生成token类
 *
 * <AUTHOR>
 * @version v1.0
 * 2020-11-13 10:13
 */
public interface TokenGenerate {

    /**
     * 生成token
     *
     * @param user     用户
     * @return TOKEN对象
     */
    Token generateToken(User user);
    /**
     * 生成token
     *
     * @param user     用户
     * @param longTerm 是否长时间有效
     * @return TOKEN对象
     */
    Token generateToken(User user, Boolean longTerm);

    /**
     * 刷新token
     *
     * @param refreshToken 刷新token
     * @return token
     */
    Token refreshToken(String refreshToken);

    /**
     * 获取用户权限
     *
     * @param authUser 用户信息
     */
    void handlerPermission(AuthUser authUser);

    /**
     * 创建pc端登录二维码
     *
     * @return 二维码信息
     */
    QRCodeLoginSessionVo createPcSession();

    /**
     * app端扫码登录
     *
     * @param token token
     * @return 扫码结果
     */
    Object appScanner(String token);

    /**
     * app端扫码登录确认
     *
     * @param token token
     * @param code 二维码
     * @return 扫码结果
     */
    boolean appSConfirm(String token, Integer code);

    /**
     * token登录
     *
     * @param token token
     * @return 登录结果
     */
    QRLoginResultVo loginWithSession(String token);
}
