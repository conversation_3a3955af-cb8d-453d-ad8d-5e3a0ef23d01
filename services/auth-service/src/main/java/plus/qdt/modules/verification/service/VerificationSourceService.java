package plus.qdt.modules.verification.service;

import plus.qdt.cache.CachePrefix;
import plus.qdt.modules.verification.entity.dos.VerificationSource;
import plus.qdt.modules.verification.entity.dto.VerificationDTO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 验证码资源维护 业务层
 *
 * <AUTHOR>
 * @since 2020/11/17 3:44 下午
 */
public interface VerificationSourceService extends IService<VerificationSource> {

    /**
     * 缓存
     */
    String VERIFICATION_CACHE = CachePrefix.VERIFICATION.getPrefix();


    /**
     * 初始化缓存
     *
     * @return
     */
    VerificationDTO initCache();

    /**
     * 获取验证缓存
     *
     * @return 验证码
     */
    VerificationDTO getVerificationCache();

    /**
     * 批量插入
     *
     * @return 验证码
     */
    void batchSaveCustom(List<VerificationSource> sourceList);
}