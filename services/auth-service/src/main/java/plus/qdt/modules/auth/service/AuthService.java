package plus.qdt.modules.auth.service;

import plus.qdt.common.security.enums.SceneEnums;
import plus.qdt.modules.member.entity.vo.UserVO;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 权限业务
 */
public interface AuthService {

    /**
     * 登出
     *
     * @param sceneEnums token角色类型
     */
    void logout(@RequestParam SceneEnums sceneEnums);

    /**
     * 获取用户当前信息
     *
     * @return
     */
    UserVO info();
}
