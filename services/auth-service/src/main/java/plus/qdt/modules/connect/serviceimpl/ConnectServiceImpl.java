package plus.qdt.modules.connect.serviceimpl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import plus.qdt.cache.Cache;
import plus.qdt.common.context.ThreadContextHolder;
import plus.qdt.common.enums.ClientTypeEnum;
import plus.qdt.common.enums.ResultCode;
import plus.qdt.common.exception.ServiceException;
import plus.qdt.common.security.AuthUser;
import plus.qdt.common.security.context.UserContext;
import plus.qdt.common.security.enums.SceneEnums;
import plus.qdt.common.security.token.Token;
import plus.qdt.common.utils.CookieUtil;
import plus.qdt.common.utils.StringUtils;
import plus.qdt.modules.connect.config.ConnectAuthEnum;
import plus.qdt.modules.connect.entity.Connect;
import plus.qdt.modules.connect.entity.dto.AuthToken;
import plus.qdt.modules.connect.entity.dto.ConnectAuthUser;
import plus.qdt.modules.connect.entity.dto.WechatMPLoginParams;
import plus.qdt.modules.connect.entity.enums.ConnectEnum;
import plus.qdt.modules.connect.mapper.ConnectMapper;
import plus.qdt.modules.connect.service.ConnectService;
import plus.qdt.modules.member.client.UserClient;
import plus.qdt.modules.member.entity.dos.User;
import plus.qdt.modules.member.entity.dto.ConnectQueryDTO;
import plus.qdt.modules.member.entity.dto.UserInfoDTO;
import plus.qdt.modules.token.TokenGenerate;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import javax.naming.NoPermissionException;
import java.nio.charset.StandardCharsets;
import java.security.AlgorithmParameters;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 联合登陆接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Lazy
public class ConnectServiceImpl extends ServiceImpl<ConnectMapper, Connect> implements ConnectService {

    static final boolean AUTO_REGION = true;

    private final UserClient userClient;

    private final Cache cache;

    private final TokenGenerate tokenGenerate;

    @Override
    public Token autoRegister() {
        ConnectAuthUser connectAuthUser = this.checkConnectUser();
        return this.autoRegister(connectAuthUser);
    }

    @Override
    public Token autoRegister(ConnectAuthUser authUser) {
        if (CharSequenceUtil.isEmpty(authUser.getNickname())) {
            authUser.setNickname("临时昵称");
        }
        if (CharSequenceUtil.isEmpty(authUser.getAvatar())) {
            authUser.setAvatar("https://i.loli.net/2020/11/19/LyN6JF7zZRskdIe.png");
        }
        try {
            User user = userClient.register(
                    new UserInfoDTO(authUser)
            );
            //绑定登录方式
            bindUser(user, authUser);
            return tokenGenerate.generateToken(user, false);
        } catch (ServiceException e) {
            log.error("自动注册服务抛出异常：", e);
            throw e;
        } catch (Exception e) {
            log.error("自动注册异常：", e);
            throw new ServiceException(ResultCode.USER_AUTO_REGISTER_ERROR);
        }
    }

    @Override
    public Token unionLoginCallback(String type, String unionid, String uuid, boolean longTerm) throws NoPermissionException {
        try {
            LambdaQueryWrapper<Connect> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Connect::getUnionId, unionid);
            queryWrapper.eq(Connect::getUnionType, type);
            //查询绑定关系
            Connect connect = this.getOne(queryWrapper);
            if (connect == null) {
                throw new NoPermissionException("未绑定用户");
            }
            //查询会员
            User member = userClient.getById(connect.getUserId());
            //如果未绑定会员，则把刚才查询到的联合登录表数据删除
            if (member == null) {
                this.remove(queryWrapper);
                throw new NoPermissionException("未绑定用户");
            }
            return tokenGenerate.generateToken(member, longTerm);
        } catch (NoPermissionException e) {
            log.error("联合登陆失败：", e);
            throw e;
        }
    }

    @Override
    @Transactional
    public User unionLoginCallback(String type, String unionid, String uuid) throws NoPermissionException {

        LambdaQueryWrapper<Connect> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Connect::getUnionId, unionid);
        queryWrapper.eq(Connect::getUnionType, type);
        //查询绑定关系
        Connect connect = this.getOne(queryWrapper);
        if (connect == null) {
            throw new NoPermissionException("未绑定用户");
        }
        return userClient.getById(connect.getUserId());
    }

    @Override
    @Transactional
    //    @ShardingTransactionType(TransactionType.BASE)
    public User unionLoginCallback(String type, ConnectAuthUser authUser, String uuid) {

        User user;
        try {
            Connect unionConnect = null;
            Connect openConnect = null;
            //查询用户
            QueryWrapper<Connect> queryWrapper = new QueryWrapper<>();
            // 如果有unionid 则查询相关信息
            if (!CharSequenceUtil.isEmpty(authUser.getToken().getUnionId())) {
                queryWrapper.eq("union_type", authUser.getSource().name());
                queryWrapper.eq("union_id", authUser.getToken().getUnionId());
                unionConnect = this.getOne(queryWrapper, false);
            }
            // openid 查询对象
            queryWrapper = new QueryWrapper<>();
            if (CharSequenceUtil.isEmpty(authUser.getToken().getOpenId())) {
                throw new ServiceException("第三方登录异常，请稍后重试");
            }
            queryWrapper.eq("union_type", authUser.getOpenIdConnectEnum().name());
            queryWrapper.eq("union_id", authUser.getToken().getOpenId());
            openConnect = this.getOne(queryWrapper, false);

            if (unionConnect == null && openConnect == null) {
                if (AUTO_REGION) {

                    user = userClient.register(
                            UserInfoDTO.builder()
                                    .nickName(authUser.getNickname())
                                    .scene(SceneEnums.MEMBER)
                                    .face(authUser.getAvatar())
                                    .build()
                    );

                    //绑定登录方式
                    bindUser(user, authUser);
                    return user;
                } else {
                    //写入cookie
                    CookieUtil.addCookie(CONNECT_UUID, uuid, 1800, ThreadContextHolder.getHttpResponse());
                    CookieUtil.addCookie(CONNECT_TYPE, type, 1800, ThreadContextHolder.getHttpResponse());
                    //自动登录失败，则把信息缓存起来
                    cache.put(ConnectService.cacheKey(type, uuid), authUser, 30L, TimeUnit.MINUTES);
                    throw new ServiceException(ResultCode.USER_NOT_BINDING);
                }
            }
            //差异数据同步处理
            dataSync(unionConnect, openConnect, authUser);

            return userClient.getById(openConnect == null ? unionConnect.getUserId() : openConnect.getUserId());

        } catch (Exception e) {
            log.error("联合登陆异常：", e);
            throw new ServiceException();
        }
    }

    //数据同步，如果unionid与openid冲突或数据不完整，做业务处理
    private void dataSync(Connect unionConnect, Connect openConnect, ConnectAuthUser authUser) {

        if (unionConnect == null && openConnect == null) {
            return;
        }
        // 如果两个登录信息都存在，切用户不一致，则以unionid为准
        if (unionConnect != null && openConnect != null && !openConnect.getUserId().equals(unionConnect.getUserId())) {
            openConnect.setUserId(unionConnect.getUserId());
            this.updateById(openConnect);
        }
        //如果openid 不存在，则以unionid为准
        if (openConnect == null && unionConnect != null) {
            openConnect = new Connect();
            openConnect.setUserId(unionConnect.getUserId());
            openConnect.setUnionId(authUser.getToken().getOpenId());
            openConnect.setUnionType(authUser.getOpenIdConnectEnum().name());
            this.save(openConnect);
        }
        //如果unionid 不存在，则以openid为准
        if (unionConnect == null && openConnect != null) {
            unionConnect = new Connect();
            unionConnect.setUserId(openConnect.getUserId());
            unionConnect.setUnionId(authUser.getToken().getUnionId());
            unionConnect.setUnionType(authUser.getSource().name());
            this.save(unionConnect);
        }

    }

    @Override
    public void bind(String unionId, String type) {
        AuthUser authUser = Objects.requireNonNull(UserContext.getCurrentUser());
        Connect connect = new Connect(authUser.getId(), unionId, type);
        this.save(connect);
    }

    @Override
    @Transactional
    //    @ShardingTransactionType(TransactionType.BASE)
    public void unbind(String type) {

        LambdaQueryWrapper<Connect> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper.eq(Connect::getUserId, Objects.requireNonNull(UserContext.getCurrentUser()).getId());
        queryWrapper.eq(Connect::getUnionType, type);

        this.remove(queryWrapper);
    }

    @Override
    public List<String> bindList() {
        LambdaQueryWrapper<Connect> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Connect::getUserId, Objects.requireNonNull(UserContext.getCurrentUser()).getId());
        List<Connect> connects = this.list(queryWrapper);
        List<String> keys = new ArrayList<>();
        connects.forEach(item -> keys.add(item.getUnionType()));
        return keys;
    }


    @Override
    @Transactional
    //    @ShardingTransactionType(TransactionType.BASE)
    public User mpBind(String sessionKey, WechatMPLoginParams params, String openId, String unionId, String promotionCode) {
        String encryptedData = params.getEncryptedData();
        String iv = params.getIv();
        JSONObject userInfo = this.getUserInfo(encryptedData, sessionKey, iv);
        log.info("联合登陆返回：{}", userInfo.toString());
        //获取手机号
        String phone = userInfo.containsKey("purePhoneNumber") ? userInfo.getStr("purePhoneNumber") : null;

        //生成联合登陆对象
        AuthToken authToken = AuthToken.builder()
                .openId(openId)
                .unionId(unionId)
                .build();
        ConnectAuthUser connectAuthUser = ConnectAuthUser.builder()
                .source(ConnectEnum.WECHAT)
                .type(ClientTypeEnum.WECHAT_MP)
                .token(authToken)
                .phone(phone)
                .avatar(userInfo.getStr("avatarUrl"))
                .nickname(userInfo.getStr("nickName"))
                .build();

        //如果有手机号，根据手机号进行登录
        User user = !StringUtils.isEmpty(phone) ? userClient.mobilePhoneLogin(phone, SceneEnums.MEMBER, promotionCode) : null;
        //如果不存在会员，则进行绑定微信openid 和 unionid，并且登录
        if (user != null) {
            //手机号查询到会员则绑定
            bindUser(user, connectAuthUser);
            return user;
        }
        //如果不存在会员，则进行注册
        user = this.unionLoginCallback(ConnectEnum.WECHAT.name(), connectAuthUser, UUID.randomUUID().toString());
        //是否有推荐码
        if (user != null) {
            userClient.insertShareLog(promotionCode, user.getId(), user.getNickName(), user.getFace());
        }
        return user;
    }

    @Override
    public Connect queryConnect(ConnectQueryDTO connectQueryDTO) {

        LambdaQueryWrapper<Connect> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(connectQueryDTO.getUserId()), Connect::getUserId, connectQueryDTO.getUserId())
                .eq(CharSequenceUtil.isNotEmpty(connectQueryDTO.getUnionType()), Connect::getUnionType, connectQueryDTO.getUnionType())
                .eq(CharSequenceUtil.isNotEmpty(connectQueryDTO.getUnionId()), Connect::getUnionId, connectQueryDTO.getUnionId());
        return this.getOne(queryWrapper);
    }

    @Override
    public void deleteByMemberId(String userId) {
        LambdaQueryWrapper<Connect> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Connect::getUserId, userId);
        this.remove(queryWrapper);
    }

    /**
     * 第三方联合登陆
     * 1.判断是否使用开放平台
     * 1.1如果使用开放平台则使用UnionId进行登录
     * 1.2如果不适用开放平台则使用OpenId进行登录
     * <p>
     * 2.用户登录后判断绑定OpenId
     *
     * @param authUser 第三方登录封装类
     * @return token
     */
    @Override
    public Token unionLoginCallback(ConnectAuthUser authUser) {
        try {
            User user = unionLoginCallback(authUser.getType().name(), authUser, UUID.randomUUID().toString());
            return tokenGenerate.generateToken(user, true);

        } catch (Exception e) {
            log.error("联合登陆失败：", e);
            throw e;
        }
    }


    /**
     * 解密，获取微信信息
     *
     * @param encryptedData 加密信息
     * @param sessionKey    微信sessionKey
     * @param iv            微信揭秘参数
     * @return 用户信息
     */
    public JSONObject getUserInfo(String encryptedData, String sessionKey, String iv) {

        log.info("encryptedData:{},sessionKey:{},iv:{}", encryptedData, sessionKey, iv);
        //被加密的数据
        byte[] dataByte = Base64.getDecoder().decode(encryptedData);
        //加密秘钥
        byte[] keyByte = Base64.getDecoder().decode(sessionKey);
        //偏移量
        byte[] ivByte = Base64.getDecoder().decode(iv);
        try {
            //如果密钥不足16位，那么就补足.  这个if 中的内容很重要
            int base = 16;
            if (keyByte.length % base != 0) {
                int groups = keyByte.length / base + (keyByte.length % base != 0 ? 1 : 0);
                byte[] temp = new byte[groups * base];
                Arrays.fill(temp, (byte) 0);
                System.arraycopy(keyByte, 0, temp, 0, keyByte.length);
                keyByte = temp;
            }
            //初始化
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding", new BouncyCastleProvider());
            SecretKeySpec spec = new SecretKeySpec(keyByte, "AES");
            AlgorithmParameters parameters = AlgorithmParameters.getInstance("AES");
            parameters.init(new IvParameterSpec(ivByte));
            //初始化
            cipher.init(Cipher.DECRYPT_MODE, spec, parameters);
            byte[] resultByte = cipher.doFinal(dataByte);
            if (null != resultByte && resultByte.length > 0) {
                String result = new String(resultByte, StandardCharsets.UTF_8);
                return JSONUtil.parseObj(result);
            }
        } catch (Exception e) {
            log.error("解密，获取微信信息错误", e);
        }
        throw new ServiceException(ResultCode.USER_CONNECT_ERROR);
    }

    /**
     * 检测是否可以绑定第三方联合登陆
     * 返回null原因
     * 包含原因1：redis中已经没有联合登陆信息  2：已绑定其他账号
     *
     * @return 返回对象则代表可以进行绑定第三方用户，返回null则表示联合登陆无法继续
     */
    private ConnectAuthUser checkConnectUser() {
        //获取cookie存储的信息
        String uuid = CookieUtil.getCookie(CONNECT_UUID, ThreadContextHolder.getHttpRequest());
        String connectType = CookieUtil.getCookie(CONNECT_TYPE, ThreadContextHolder.getHttpRequest());

        //如果联合登陆存储了信息
        if (CharSequenceUtil.isNotEmpty(uuid) && CharSequenceUtil.isNotEmpty(connectType)) {
            //枚举 联合登陆类型获取
            ConnectAuthEnum authInterface = ConnectAuthEnum.valueOf(connectType);

            ConnectAuthUser connectAuthUser = getConnectAuthUser(uuid, connectType);
            if (connectAuthUser == null) {
                throw new ServiceException(ResultCode.USER_OVERDUE_CONNECT_ERROR);
            }
            //检测是否已经绑定过用户
            Connect connect = this.queryConnect(ConnectQueryDTO.builder().unionType(connectType).unionId(connectAuthUser.getUuid()).build());
            //没有关联则返回true，表示可以继续绑定
            if (connect == null) {
                connectAuthUser.setConnectEnum(authInterface);
                return connectAuthUser;
            } else {
                throw new ServiceException(ResultCode.USER_CONNECT_BANDING_ERROR);
            }
        } else {
            throw new ServiceException(ResultCode.USER_CONNECT_NOT_EXIST_ERROR);
        }
    }


    /**
     * 获取cookie中的联合登录对象
     *
     * @param uuid uuid
     * @param type 状态
     * @return cookie中的联合登录对象
     */
    private ConnectAuthUser getConnectAuthUser(String uuid, String type) {
        Object context = cache.get(ConnectService.cacheKey(type, uuid));
        if (context != null) {
            return (ConnectAuthUser) context;
        }
        return null;
    }

    /**
     * 成功登录，则检测cookie中的信息，进行用户绑定
     *
     * @param member   用户
     * @param authUser 联合登录信息
     */
    private void bindUser(User member, ConnectAuthUser authUser) {
        switch (authUser.getSource()) {
            case WECHAT:
                bindUser(member, authUser.getToken().getUnionId(), ConnectEnum.WECHAT.name());
                break;
            case QQ:
                bindUser(member, authUser.getToken().getUnionId(), ConnectEnum.QQ.name());
                break;
            default:
                break;
        }

        bindUser(member, authUser.getToken().getOpenId(), authUser.getOpenIdConnectEnum().name());
    }

    /**
     * 成功登录，则检测cookie中的信息，进行用户绑定
     *
     * @param member  用户
     * @param unionId unionId
     * @param type    状态
     */
    private void bindUser(User member, String unionId, String type) {
        if (StringUtils.isEmpty(unionId) || StringUtils.isEmpty(type) || member == null) {
            return;
        }
        Connect connect = this.queryConnect(ConnectQueryDTO.builder().unionId(unionId).unionType(type).build());
        if (connect == null) {
            connect = new Connect(member.getId(), unionId, type);
            this.save(connect);
        } else if (!connect.getUserId().equals(member.getId())) {
            // 如果库中用户ID与当前用户ID不匹配，那么则绑定
            connect.setUserId(member.getId());
            this.updateById(connect);
        }
    }


}