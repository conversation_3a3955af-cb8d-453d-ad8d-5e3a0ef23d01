package plus.qdt.controller.auth;


import plus.qdt.common.enums.ResultCode;
import plus.qdt.common.exception.ServiceException;
import plus.qdt.common.security.token.Token;
import plus.qdt.common.utils.CommonUtil;
import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.connect.entity.dto.AuthCallback;
import plus.qdt.modules.connect.entity.dto.ConnectAuthUser;
import plus.qdt.modules.connect.request.AuthRequest;
import plus.qdt.modules.connect.service.ConnectService;
import plus.qdt.modules.connect.util.ConnectHandler;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

/**
 * 买家端,web联合登录
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "买家端,web联合登录")
@RequestMapping("/auth/connect")
@RequiredArgsConstructor
public class ConnectWebController {

    private final ConnectService connectService;

    private final ConnectHandler connectHandler;


    @GetMapping("/login/web/{type}")
    @Operation(summary = "WEB信任登录授权")
    @Parameter(name = "type", description = "登录方式:QQ,微信,微信_PC")
    public ResultMessage<String> webAuthorize(@PathVariable String type, HttpServletResponse response) throws IOException {
        AuthRequest authRequest = connectHandler.getAuthRequest(type);
        String authorizeUrl = authRequest.authorize(CommonUtil.getUUID());
        response.sendRedirect(authorizeUrl);
        return ResultUtil.data(authorizeUrl);
    }


    @Operation(summary = "信任登录统一回调地址", hidden = true)
    @GetMapping("/callback/{type}")
    public void callBack(@PathVariable String type, AuthCallback callback, HttpServletRequest httpServletRequest,
                         HttpServletResponse httpServletResponse) {
        connectHandler.callback(type, callback, httpServletRequest, httpServletResponse);
    }

    @Operation(summary = "信任登录响应结果获取")
    @GetMapping("/result")
    public ResultMessage<Object> callBackResult(String state) {
        if (state == null) {
            throw new ServiceException(ResultCode.USER_CONNECT_LOGIN_ERROR);
        }
        return connectHandler.getResult(state);
    }

    @GetMapping("/register/auto")
    @Operation(summary = "WEB信任登录授权")
    public ResultMessage<Token> webAuthorize() {
        Token token = connectService.autoRegister();
        return ResultUtil.data(token);
    }

    @Operation(summary = "APP unionID 登录")
    @PostMapping("/app/login")
    public ResultMessage<Token> unionLogin(@RequestBody ConnectAuthUser authUser) {
        try {
            return ResultUtil.data(connectService.unionLoginCallback(authUser));
        } catch (Exception e) {
            log.error("unionID登录错误", e);
        }
        return null;
    }

}
