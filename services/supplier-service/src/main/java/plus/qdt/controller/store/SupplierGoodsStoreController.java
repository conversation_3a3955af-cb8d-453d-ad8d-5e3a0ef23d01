package plus.qdt.controller.store;

import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.vo.PageVO;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.goods.client.GoodsClient;
import plus.qdt.modules.goods.entity.dto.ProxyGoodsOperationDTO;
import plus.qdt.modules.search.client.EsGoodsSearchClient;
import plus.qdt.modules.search.entity.dos.EsGoodsIndex;
import plus.qdt.modules.search.entity.dos.EsGoodsRelatedInfo;
import plus.qdt.modules.search.entity.dto.CustomSearchParams;
import plus.qdt.modules.search.entity.dto.EsGoodsSearchDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.elasticsearch.core.SearchPage;
import org.springframework.web.bind.annotation.*;

/**
 * 店铺端,商品接口
 *
 * <AUTHOR>
 * @since 2020-02-23 15:18:56
 */
@RestController
@Tag(name = "商家端端,供应商商品接口")
@RequestMapping("/supplier/store/goods")
@RequiredArgsConstructor
public class SupplierGoodsStoreController {

    private final GoodsClient goodsClient;

    private final EsGoodsSearchClient esGoodsSearchClient;

    @Operation(summary = "代理商品")
    @PostMapping("/proxy")
    public ResultMessage<Object> getGoodsRelatedByPageFromEs(String goodsId) {
        //供应商根据商品id，代理商品
        goodsClient.addSupplierGoods(goodsId);
        return ResultUtil.success();
    }

    @Operation(summary = "店铺修改代理商品")
    @PutMapping("/edit")
    public ResultMessage<Object> proxyUpdate(@RequestBody ProxyGoodsOperationDTO proxyGoodsOperationDTO) {
        goodsClient.editProxyGoods(proxyGoodsOperationDTO);
        return ResultUtil.success();
    }

    @Operation(summary = "从ES中获取商品信息")
    @GetMapping("/es")
    public ResultMessage<SearchPage<EsGoodsIndex>> getGoodsByPageFromEs(EsGoodsSearchDTO goodsSearchParams,
                                                                        PageVO pageVO) {
        pageVO.setNotConvert(true);
        CustomSearchParams customSearchParams = new CustomSearchParams();
        customSearchParams.setGoodsSearchParams(goodsSearchParams);
        customSearchParams.setPageVO(pageVO);
        SearchPage<EsGoodsIndex> esGoodsIndices = esGoodsSearchClient.customSearch(customSearchParams);
        return ResultUtil.data(esGoodsIndices);
    }

    @Operation(summary = "从ES中获取相关商品品牌名称，分类名称及属性")
    @GetMapping("/es/related")
    public ResultMessage<EsGoodsRelatedInfo> getGoodsRelatedByPageFromEs(EsGoodsSearchDTO goodsSearchParams,
                                                                         PageVO pageVO) {
        pageVO.setNotConvert(true);
        CustomSearchParams customSearchParams = new CustomSearchParams();
        customSearchParams.setGoodsSearchParams(goodsSearchParams);
        customSearchParams.setPageVO(pageVO);
        EsGoodsRelatedInfo selector = esGoodsSearchClient.related(customSearchParams);
        return ResultUtil.data(selector);
    }

}
