package plus.qdt.modules.supplier.serviceimpl;

import plus.qdt.common.enums.ResultCode;
import plus.qdt.common.exception.ServiceException;
import plus.qdt.common.security.context.UserContext;
import plus.qdt.modules.order.order.client.OrderClient;
import plus.qdt.modules.order.order.entity.dos.Order;
import plus.qdt.modules.order.order.entity.dto.PartDeliveryParamsDTO;
import plus.qdt.modules.supplier.service.SupplierOrderService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class SupplierOrderServiceImpl implements SupplierOrderService {


    private final OrderClient orderClient;

    @Override
    public List<Order> cancel(String orderSn, String reason) {

        Order order = orderClient.getBySn(orderSn);

        if (order.getSupplierId().equals(UserContext.getCurrentId())) {
            return orderClient.cancel(orderSn, reason);
        } else {
            throw new ServiceException(ResultCode.USER_AUTHORITY_ERROR);
        }
    }

    @Override
    public Order delivery(String orderSn, String invoiceNumber, String logisticsId) {

        Order order = orderClient.getBySn(orderSn);

        if (order.getSupplierId().equals(UserContext.getCurrentId())) {

            return orderClient.delivery(orderSn, invoiceNumber, logisticsId);
        } else {
            throw new ServiceException(ResultCode.USER_AUTHORITY_ERROR);
        }
    }

    @Override
    public Order partDelivery(PartDeliveryParamsDTO partDeliveryParamsDTO) {
        Order order = orderClient.getBySn(partDeliveryParamsDTO.getOrderSn());

        if (order.getSupplierId().equals(UserContext.getCurrentId())) {

            return orderClient.partDelivery(partDeliveryParamsDTO);
        } else {
            throw new ServiceException(ResultCode.USER_AUTHORITY_ERROR);
        }
    }
}
