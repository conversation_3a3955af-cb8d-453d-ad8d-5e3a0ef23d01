package plus.qdt.modules.supplier.service;

import plus.qdt.modules.goods.entity.dos.Goods;
import plus.qdt.modules.goods.entity.dto.GoodsSearchParams;
import plus.qdt.modules.supplier.entity.dto.SupplierGoodsOperationDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 供应商商品 业务层
 *
 * <AUTHOR>
 */
public interface SupplierGoodsService {

    /**
     * 修改供应商商品商品
     *
     * @param supplierGoodsOperationDTO 商品属性
     */
    void editGoods(SupplierGoodsOperationDTO supplierGoodsOperationDTO);

    /**
     * 增加供应商商品
     *
     * @param supplierGoodsOperationDTO 供应商类型
     */
    void addGoods(SupplierGoodsOperationDTO supplierGoodsOperationDTO);

    /**
     * 删除商品
     *
     * @param goodsId 商品id
     * @return
     */
    void deleteGoods(List<String> goodsId);
}