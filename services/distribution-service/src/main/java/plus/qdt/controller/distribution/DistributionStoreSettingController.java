package plus.qdt.controller.distribution;

import plus.qdt.common.security.AuthUser;
import plus.qdt.common.security.context.UserContext;
import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.distribution.entity.dos.DistributionStore;
import plus.qdt.modules.distribution.entity.dto.DistributionStoreDTO;
import plus.qdt.modules.distribution.service.DistributionStoreService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * @Author: Bulbasaur
 * @CreateTime: 2023-10-07  14:08
 * @Description: 店铺端，分销店铺设置API
 * @Version: 1.0
 */
@RestController
@Tag(name = "店铺端,分销设置接口")
@RequestMapping("/distribution/store/setting")
@RequiredArgsConstructor
public class DistributionStoreSettingController {

    private final DistributionStoreService distributionStoreService;

    @Operation(summary = "获取店铺分销设置")
    @GetMapping
    public ResultMessage<DistributionStore> get() {
        AuthUser currentUser = Objects.requireNonNull(UserContext.getCurrentUser());
        //获取当前登录的店铺分销设置相关信息你
        return ResultUtil.data(distributionStoreService.getByStoreId(currentUser.getExtendId()));
    }

    @Operation(summary = "修改店铺分销设置")
    @PutMapping
    public ResultMessage<Object> edit(DistributionStoreDTO distributionStoreDTO) {
        AuthUser currentUser = Objects.requireNonNull(UserContext.getCurrentUser());
        distributionStoreDTO.validateParams();
        //修改店铺分销设置
        distributionStoreService.updateDistributionStore(currentUser.getExtendId(), currentUser.getExtendName(), distributionStoreDTO);
        return ResultUtil.success();
    }
}
