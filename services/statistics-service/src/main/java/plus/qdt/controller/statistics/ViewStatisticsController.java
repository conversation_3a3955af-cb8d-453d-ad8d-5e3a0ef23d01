package plus.qdt.controller.statistics;

import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.member.entity.vo.MemberDistributionVO;
import plus.qdt.modules.statistics.entity.dto.StatisticsQueryParam;
import plus.qdt.modules.statistics.entity.vo.OnlineMemberVO;
import plus.qdt.modules.statistics.entity.vo.PlatformViewVO;
import plus.qdt.modules.statistics.service.PlatformViewService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 流量统计接口
 *
 * <AUTHOR>
 * @since 2021/2/9 11:19
 */
@Tag(name = "流量统计接口")
@RestController
@RequestMapping("/statistics/view")
@RequiredArgsConstructor
public class ViewStatisticsController {

    private final PlatformViewService platformViewService;

    @Operation(summary = "流量数据 表单获取")
    @GetMapping("/list")
    public ResultMessage<List<PlatformViewVO>> getByPage(StatisticsQueryParam queryParam) {
        return ResultUtil.data(platformViewService.list(queryParam));
    }

    @Operation(summary = "当前在线人数")
    @GetMapping("/online/current")
    public ResultMessage<Long> currentNumberPeopleOnline() {
        return ResultUtil.data(platformViewService.online());
    }


    @Operation(summary = "会员分布")
    @GetMapping("/online/distribution")
    public ResultMessage<List<MemberDistributionVO>> memberDistribution() {
        return ResultUtil.data(platformViewService.memberDistribution());
    }

    @Operation(summary = "在线人数历史（默认48小时）")
    @GetMapping("/online/history")
    public ResultMessage<List<OnlineMemberVO>> history() {
        return ResultUtil.data(platformViewService.statisticsOnline());
    }

}
