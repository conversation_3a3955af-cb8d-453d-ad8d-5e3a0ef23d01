package plus.qdt.controller.statistics;

import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.order.order.client.OrderFlowStatisticsClient;
import plus.qdt.modules.statistics.entity.dto.GoodsStatisticsQueryParam;
import plus.qdt.modules.statistics.entity.vo.CategoryStatisticsDataVO;
import plus.qdt.modules.statistics.entity.vo.GoodsStatisticsDataVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 商品统计接口
 *
 * <AUTHOR>
 * @since 2020/12/9 19:04
 */
@Tag(name = "商品流水统计接口")
@RestController
@RequestMapping("/statistics/goods")
@RequiredArgsConstructor
public class GoodsStatisticsController {

    private final OrderFlowStatisticsClient orderFlowStatisticsClient;

    @Operation(summary = "获取统计列表,排行前一百的数据")
    @GetMapping
    public ResultMessage<List<GoodsStatisticsDataVO>> getByPage(GoodsStatisticsQueryParam goodsStatisticsQueryParam) {
        return ResultUtil.data(orderFlowStatisticsClient.getGoodsStatisticsData(goodsStatisticsQueryParam, 100));
    }

    @Operation(summary = "获取行业统计列表")
    @GetMapping("/getCategoryByPage")
    public ResultMessage<List<CategoryStatisticsDataVO>> getCategoryByPage(GoodsStatisticsQueryParam goodsStatisticsQueryParam) {
        return ResultUtil.data(orderFlowStatisticsClient.getCategoryStatisticsData(goodsStatisticsQueryParam));
    }
}
