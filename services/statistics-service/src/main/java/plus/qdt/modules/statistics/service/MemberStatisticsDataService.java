package plus.qdt.modules.statistics.service;

import plus.qdt.modules.statistics.entity.dos.MemberStatisticsData;
import plus.qdt.modules.statistics.entity.dto.StatisticsQueryParam;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 会员统计业务层
 *
 * <AUTHOR>
 * @since 2020/12/9 11:06
 */
public interface MemberStatisticsDataService extends IService<MemberStatisticsData> {

    /**
     * 根据参数，查询这段时间的会员统计
     *
     * @param statisticsQueryParam
     * @return
     */
    List<MemberStatisticsData> statistics(StatisticsQueryParam statisticsQueryParam);

}