package plus.qdt.controller.goods;

import plus.qdt.common.enums.ResultCode;
import plus.qdt.common.exception.ServiceException;
import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.goods.entity.dos.Brand;
import plus.qdt.modules.goods.entity.dto.BrandPageDTO;
import plus.qdt.modules.goods.entity.vos.BrandVO;
import plus.qdt.modules.goods.service.BrandService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 品牌接口
 *
 * <AUTHOR>
 * @since 2020-02-18 15:18:56
 */
@RestController
@Tag(name = "品牌接口")
@RequestMapping("/goods/brand")
@RequiredArgsConstructor
public class BrandController {

    private final BrandService brandService;

    @Operation(summary = "通过id获取")
    @GetMapping(value = "/get/{id}")
    public ResultMessage<Brand> get(@NotNull @PathVariable String id) {
        return ResultUtil.data(brandService.getById(id));
    }

    @GetMapping(value = "/all")
    @Operation(summary = "获取所有可用品牌")
    public ResultMessage<List<Brand>> getAll() {
        return ResultUtil.data(brandService.list(new QueryWrapper<Brand>().eq("delete_flag", 0)));
    }

    @Operation(summary = "分页获取")
    @GetMapping(value = "/getByPage")
    public ResultMessage<Page<Brand>> getByPage(BrandPageDTO page) {
        return ResultUtil.data(brandService.getBrandsByPage(page));
    }

    @Operation(summary = "新增品牌")
    @PostMapping
    public ResultMessage<BrandVO> save(@Valid BrandVO brand) {
        if (brandService.addBrand(brand)) {
            return ResultUtil.data(brand);
        }
        throw new ServiceException(ResultCode.BRAND_SAVE_ERROR);
    }

    @Operation(summary = "更新数据")
    @PutMapping("/{id}")
    public ResultMessage<BrandVO> update(@PathVariable String id, @Valid BrandVO brand) {
        brand.setId(id);
        if (brandService.updateBrand(brand)) {
            return ResultUtil.data(brand);
        }
        throw new ServiceException(ResultCode.BRAND_UPDATE_ERROR);
    }

    @Operation(summary = "后台禁用品牌")
    @PutMapping(value = "/disable/{brandId}")
    public ResultMessage<Object> disable(@PathVariable String brandId, @RequestParam Boolean disable) {
        if (brandService.brandDisable(brandId, disable)) {
            return ResultUtil.success();
        }
        throw new ServiceException(ResultCode.BRAND_DISABLE_ERROR);
    }

    @Operation(summary = "批量删除")
    @DeleteMapping(value = "/delByIds/{ids}")
    public ResultMessage<Object> delAllByIds(@PathVariable List<String> ids) {
        brandService.deleteBrands(ids);
        return ResultUtil.success();
    }

}
