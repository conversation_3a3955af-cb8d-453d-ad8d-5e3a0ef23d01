package plus.qdt.modules.goods.service;


import plus.qdt.modules.goods.entity.dos.GoodsUnit;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 商品计量单位业务层
 *
 * <AUTHOR>
 * @since 2020/11/26 16:12
 */
public interface GoodsUnitService extends IService<GoodsUnit> {

    /**
     * 保存商品计量单位
     *
     * @param goodsUnit 商品计量单位
     */
    void saveGoodsUnit(GoodsUnit goodsUnit);

    /**
     * 更新商品计量单位
     *
     * @param goodsUnit 商品计量单位
     */
    void updateGoodsUnit(GoodsUnit goodsUnit);

}