package plus.qdt.modules.goods.service;

import plus.qdt.cache.CachePrefix;
import plus.qdt.modules.goods.entity.dos.Goods;
import plus.qdt.modules.goods.entity.dos.GoodsSku;
import plus.qdt.modules.goods.entity.dto.GoodsSearchParams;
import plus.qdt.modules.goods.entity.dto.GoodsSkuDTO;
import plus.qdt.modules.goods.entity.dto.YZHGoodsSkuDTO;
import plus.qdt.modules.goods.entity.vos.CategoryVO;
import plus.qdt.modules.goods.entity.vos.GoodsSkuVO;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 商品sku业务层
 *
 * <AUTHOR>
 * @since 2020-02-24 16:18:56
 */
public interface GoodsSkuService extends IService<GoodsSku> {

    /**
     * 获取商品SKU缓存ID
     *
     * @param id SkuId
     * @return 商品SKU缓存ID
     */
    static String getCacheKeys(String id) {
        return CachePrefix.GOODS_SKU.getPrefix() + id;
    }

    /**
     * 批量更新
     *
     * @param skuList 商品sku信息
     */
    void updateBatch(List<GoodsSku> skuList);

    /**
     * 更新商品sku
     *
     * @param goodsSku sku信息
     */
    void update(GoodsSku goodsSku);

    /**
     * 清除sku缓存
     *
     * @param skuId skuid
     */
    void clearCache(String skuId);

    /**
     * 从redis缓存中获取商品SKU信息
     *
     * @param id SkuId
     * @return 商品SKU信息
     */
    GoodsSku getGoodsSkuByIdFromCache(String id);


    /**
     * 批量从redis中获取商品SKU信息
     *
     * @param ids SkuId集合
     * @return 商品SKU信息集合
     */
    List<GoodsSku> getGoodsSkuByIdFromCache(List<String> ids);

    /**
     * 获取goodsId下所有的goodsSku
     *
     * @param searchParams 查询参数
     * @return goodsSku列表
     */
    List<GoodsSkuVO> getGoodsListByGoodsId(GoodsSearchParams searchParams);


    /**
     * 获取goodsId下所有的goodsSku
     *
     * @param goodsId 商品id
     * @return goodsSku列表
     */
    List<GoodsSku> getGoodsSkuListByGoodsId(String goodsId);


    /**
     * 获取goodsId下所有的goodsSku
     *
     * @param goodsIds 商品id
     * @return goodsSku列表
     */
    List<GoodsSku> getGoodsSkuListByGoodsId(List<String> goodsIds);

    /**
     * 获取goodsId下所有的goodsSku
     *
     * @param searchParams 查询参数
     * @return goodsSku列表
     */
    List<String> getGoodsSkuIdListByGoodsId(GoodsSearchParams searchParams);

    /**
     * 根据goodsSku组装goodsSkuVO
     *
     * @param list 商品id
     * @return goodsSku列表
     */
    List<GoodsSkuVO> getGoodsSkuVOList(List<GoodsSku> list);

    /**
     * 根据goodsSku组装goodsSkuVO
     *
     * @param goodsSku 商品规格
     * @return goodsSku列表
     */
    GoodsSkuVO getGoodsSkuVO(GoodsSku goodsSku);

    /**
     * 分页查询商品sku信息
     *
     * @param searchParams 查询参数
     * @return 商品sku信息
     */
    Page<GoodsSku> getGoodsSkuByPage(GoodsSearchParams searchParams);


    /**
     * 分页查询商品sku信息
     *
     * @param page         分页参数
     * @param queryWrapper 查询参数
     * @return 商品sku信息
     */
    Page<GoodsSkuDTO> getGoodsSkuDTOByPage(Page<GoodsSkuDTO> page, Wrapper<GoodsSkuDTO> queryWrapper);

    /**
     * 列表查询商品sku信息
     *
     * @param searchParams 查询参数
     * @return 商品sku信息
     */
    List<GoodsSku> getGoodsSkuByList(GoodsSearchParams searchParams);

    /**
     * 更新商品sku状态
     *
     * @param goods 商品信息(Id,MarketEnable/AuthFlag)
     */
    void updateGoodsSkuStatus(Goods goods);

    /**
     * 更新商品sku状态根据店铺id
     *
     * @param storeId      店铺id
     * @param marketEnable 市场启用状态
     * @param authFlag     审核状态
     */
    void updateGoodsSkuStatusByStoreId(String storeId, String marketEnable, String authFlag);

    /**
     * 更新SKU库存
     *
     * @param goodsSkus 商品sku集合
     */
    void updateStock(List<GoodsSku> goodsSkus);

    /**
     * 更新SKU
     *
     * @param goodsSku 商品sku
     */
    boolean updateGoodsSkuBatch(List<GoodsSku> skuList,String goodsId);

    /**
     * 获取商品sku库存
     *
     * @param skuId 商品skuId
     * @return 库存数量
     */
    Integer getStock(String skuId);

    /**
     * 获取最新商品库存
     *
     * @param goodsId 商品ID
     * @return 库存数量
     */
    Integer getGoodsStock(String goodsId);


    /**
     * 根据商品id获取全部skuId的集合
     *
     * @param goodsId goodsId
     * @return 全部skuId的集合
     */
    List<String> getSkuIdsByGoodsId(String goodsId);

    /**
     * 删除并且新增sku，即覆盖之前信息
     *
     * @param goodsSkus 商品sku集合
     * @return
     */
    boolean deleteAndInsertGoodsSkus(List<GoodsSku> goodsSkus);

    /**
     * 根据商品id删除sku
     *
     * @param goodsId 商品id
     * @return
     */
    boolean deleteByGoodsId(String goodsId);

    /**
     * 统计sku总数
     *
     * @param storeId 店铺id
     * @return sku总数
     */
    Long countSkuNum(String storeId);

    /**
     * 更新商品sku评分
     *
     * @param goodsId 商品ID
     * @param grade   评分
     * @param commentNum 评论数量
     */
    void updateGoodsSkuGrade(String goodsId, double grade,int commentNum);

    /***
     * 保存商品sku
         * @param goodsDetailList 商品详情集合
     * @return void
     * <AUTHOR>
     */
    void saveGoodsDetails(List<YZHGoodsSkuDTO> goodsDetailList, List<CategoryVO> categoryVOS);
}