package plus.qdt.modules.goods.service;

import plus.qdt.modules.goods.entity.dos.Goods;
import plus.qdt.modules.goods.entity.enums.GoodsAuthEnum;
import plus.qdt.modules.goods.entity.enums.GoodsMarketEnum;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 商品统计业务层
 *
 * <AUTHOR>
 * @since 2020/12/9 11:06
 */
public interface GoodsStatisticsService extends IService<Goods> {

    /**
     * 获取所有的已上架的商品数量
     *
     * @param goodsMarketEnum 商品状态枚举
     * @param goodsAuthEnum   商品审核枚举
     * @param storeId 店铺id
     * @return 所有的已上架的商品数量
     */
    long goodsNum(GoodsMarketEnum goodsMarketEnum, GoodsAuthEnum goodsAuthEnum, String storeId);

    /**
     * 获取今天的已上架的商品数量
     *
     * @return 今天的已上架的商品数量
     */
    long todayUpperNum();
}