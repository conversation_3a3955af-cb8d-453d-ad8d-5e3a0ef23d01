package plus.qdt.modules.goods.task.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import plus.qdt.modules.goods.task.enums.ApiUrlEnum;
import plus.qdt.modules.goods.task.enums.DomainEnum;
import plus.qdt.modules.goods.task.enums.FaultEnum;
import plus.qdt.modules.goods.task.exception.ExceptionBase;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

public class RequestUtil {

    public static final String APP_ID = "517405-c871MPXeZHphayzG";
    public static final String SECRET = "mUi0q81coX4koDmEo14LO5JmT81b1mSx";
    private static final DomainEnum domain = DomainEnum.PRODUCTION;

    /**
     * 初始化快递数据
     */
    public RequestUtil() {

    }

    /**
     * 创建订单
     * @param params
     * @param domain
     * @return
     */
    public static JSONObject createOrder(Map<String, Object> params, DomainEnum domain) {
        return requestApi(ApiUrlEnum.CREATE_ORDER.getUrl(), params, domain);
    }

    /**
     * 订单变更通知
     * @param params
     * @param domain
     * @return
     */
    public static JSONObject queryOrderChangeEvent(Map<String, Object> params, DomainEnum domain) {
        return requestApi(ApiUrlEnum.QUERY_ORDER_CHANGE_EVENT.getUrl(), params, domain);
    }


    /**
     * 商品变更
     * @return
     */
    public static JSONObject queryGoodsChangeEvent(){
        Map<String, Object> params = new HashMap<>();
        params.put("appid", APP_ID);
        params.put("secret", SECRET);
        return requestApi(ApiUrlEnum.QUERY_GOODS_CHANGE_EVENT.getUrl(), params, domain);
    }
    /**
     * 请求接口
     * @param url
     * @param params
     * @param domain
     * @return
     */
    private static JSONObject requestApi(String url, Map<String, Object> params, DomainEnum domain) {
        if (params == null) {
            throw new ExceptionBase(FaultEnum.REQUEST_PARAMETER_ERROR);
        } else {
            Object appId = params.get("appid");
            Object secret = params.get("secret");
            params.remove("secret");
            params.remove("appid");
            if (!isEmpty(appId) && !isEmpty(secret)) {
                try {
                    Map<String, Object> signMap = new HashMap();
                    signMap.put("timestamp", String.valueOf(System.currentTimeMillis()));
                    signMap.put("nonce", SignUtil.generateRandomString(16));
                    signMap.put("appid", appId.toString());
                    signMap.put("signature", SignUtil.signatureInvalid(signMap, secret.toString()));
                    url = MessageFormat.format(url + "?signature={0}&nonce={1}&timestamp={2}&appid={3}", signMap.get("signature"), signMap.get("nonce"), signMap.get("timestamp"), signMap.get("appid"));
                } catch (Exception var6) {
                    var6.printStackTrace();
                    throw new ExceptionBase(FaultEnum.SECURITY_EXCEPTION);
                }

                String result = HttpClientUtil.doPost(domain.getHost() + url, params);
                return JSON.parseObject(result);
            } else {
                throw new ExceptionBase(FaultEnum.MISSING_REQUEST_PARAMETERS);
            }
        }
    }

    private static boolean isEmpty(Object obj) {
        if (obj == null) {
            return true;
        } else if (obj instanceof String) {
            String str = (String) obj;
            return str.length() < 1;
        } else {
            return false;

        }
    }


}
