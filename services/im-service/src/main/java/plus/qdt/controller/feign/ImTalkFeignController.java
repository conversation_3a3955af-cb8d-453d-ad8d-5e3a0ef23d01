package plus.qdt.controller.feign;

import plus.qdt.modules.im.client.ImTalkClient;
import plus.qdt.modules.im.entity.dos.ImTalk;
import plus.qdt.modules.im.service.ImTalkService;
import plus.qdt.modules.member.entity.dos.User;
import plus.qdt.modules.store.entity.dos.Store;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 聊天业务实现feign
 *
 * <AUTHOR>
 * @since 2022/6/2114:46
 */
@RestController
@RequiredArgsConstructor
public class ImTalkFeignController implements ImTalkClient {

    private final ImTalkService imTalkService;

    @Override
    public void updateUserInfo(User user) {
        List<ImTalk> list = imTalkService.list(new LambdaQueryWrapper<ImTalk>().eq(ImTalk::getUserId1, user.getId()).or().eq(ImTalk::getUserId2, user.getId()));
        for (ImTalk imTalk : list) {
            if(imTalk.getUserId1().equals(user.getId())){
                imTalk.setName1(user.getNickName());
                imTalk.setFace1(user.getFace());
            }else if(imTalk.getUserId2().equals(user.getId())){
                imTalk.setName2(user.getNickName());
                imTalk.setFace2(user.getFace());
            }
        }
        imTalkService.updateBatchById(list);
    }

    @Override
    public void updateStoreInfo(Store store) {
        List<ImTalk> list = imTalkService.list(new LambdaQueryWrapper<ImTalk>().eq(ImTalk::getUserId1, store.getId()).or().eq(ImTalk::getUserId2, store.getId()));
        for (ImTalk imTalk : list) {
            if(imTalk.getUserId1().equals(store.getId())){
                imTalk.setName1(store.getStoreName());
                imTalk.setFace1(store.getStoreLogo());
            }else if(imTalk.getUserId2().equals(store.getId())){
                imTalk.setName2(store.getStoreName());
                imTalk.setFace2(store.getStoreLogo());
            }
        }
        imTalkService.updateBatchById(list);
    }
}
