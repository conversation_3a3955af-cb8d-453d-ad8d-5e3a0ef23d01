package plus.qdt.event.impl;

import plus.qdt.common.utils.CurrencyUtil;
import plus.qdt.common.utils.GsonUtils;
import plus.qdt.event.GoodsCommentCompleteEvent;
import plus.qdt.event.OrderStatusChangeEvent;
import plus.qdt.event.UserRegisterEvent;
import plus.qdt.modules.member.client.UserClient;
import plus.qdt.modules.member.entity.dos.MemberEvaluation;
import plus.qdt.modules.member.entity.dos.User;
import plus.qdt.modules.order.order.client.OrderClient;
import plus.qdt.modules.order.order.entity.dos.Order;
import plus.qdt.modules.order.order.entity.dto.OrderMessage;
import plus.qdt.modules.order.order.entity.enums.OrderStatusEnum;
import plus.qdt.modules.payment.client.WalletPointClient;
import plus.qdt.modules.system.client.SettingClient;
import plus.qdt.modules.system.entity.dos.Setting;
import plus.qdt.modules.system.entity.dto.ExperienceSetting;
import plus.qdt.modules.system.entity.enums.SettingEnum;
import lombok.RequiredArgsConstructor;

/**
 * 会员经验值
 *
 * <AUTHOR>
 * @since 2021/5/16 11:16 下午
 */
@RequiredArgsConstructor
// @Service
public class UserExperienceExecute
        implements UserRegisterEvent, GoodsCommentCompleteEvent, OrderStatusChangeEvent {

    /**
     * 配置
     */
    private final SettingClient settingClient;
    /**
     * 会员
     */
    private final UserClient userClient;
    /**
     * 订单
     */
    private final OrderClient orderClient;
    private final WalletPointClient walletPointClient;

    /**
     * 会员注册赠送经验值
     *
     * @param member 会员
     */
    @Override
    public void userRegister(User member) {
        // 获取经验值设置
        ExperienceSetting experienceSetting = getExperienceSetting();
        // 赠送会员经验值
//        walletPointClient.updateMemberPoint(
//                UserPointUpdateDTO.builder()
//                        .userId(member.getId())
//                        .points(experienceSetting.getRegister().longValue())
//                        .userPointServiceEnum(UserPointServiceEnum.SIGN)
//                        .description("会员注册，赠送经验值" + experienceSetting.getRegister()).build());
    }

    /**
     * 商品评价赠送经验值
     *
     * @param memberEvaluation 会员评价
     */
    @Override
    public void goodsComment(MemberEvaluation memberEvaluation) {
        // 获取经验值设置
        ExperienceSetting experienceSetting = getExperienceSetting();
        // 赠送会员经验值
//        walletPointClient.updateMemberPoint(
//                UserPointUpdateDTO.builder()
//                        .userId(memberEvaluation.getMemberId())
//                        .points(experienceSetting.getComment().longValue())
//                        .userPointServiceEnum(UserPointServiceEnum.FULL_DISCOUNT_GIFT)
//                        .description("会员评价，赠送经验值" + experienceSetting.getComment()).build()
//                );
    }

    /**
     * 完成订单赠送经验值
     *
     * @param orderMessage 订单消息
     */
    @Override
    public void orderChange(OrderMessage orderMessage) {
        if (orderMessage.getNewStatus().equals(OrderStatusEnum.COMPLETED)) {
            // 获取经验值设置
            ExperienceSetting experienceSetting = getExperienceSetting();
            // 获取订单信息
            Order order = orderClient.getBySn(orderMessage.getOrderSn());
            // 计算赠送经验值数量
            Double point = CurrencyUtil.mul(experienceSetting.getMoney(), order.getFlowPrice(), 0);
            // 赠送会员经验值
//            walletPointClient.updateMemberPoint(
//                    UserPointUpdateDTO.builder()
//                            .userId(order.getMemberId())
//                            .points(point.longValue())
//                            .userPointServiceEnum(UserPointServiceEnum.FULL_DISCOUNT_GIFT)
//                            .description("会员下单，赠送经验值" + point + "分").build());
        }
    }

    /**
     * 获取经验值设置
     *
     * @return 经验值设置
     */
    private ExperienceSetting getExperienceSetting() {
        Setting setting = settingClient.get(SettingEnum.EXPERIENCE_SETTING.name());
        return GsonUtils.fromJson(setting.getSettingValue(), ExperienceSetting.class);
    }
}
