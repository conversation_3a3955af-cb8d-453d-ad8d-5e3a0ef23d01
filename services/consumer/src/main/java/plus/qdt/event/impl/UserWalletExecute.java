package plus.qdt.event.impl;

import plus.qdt.common.security.enums.SceneEnums;
import plus.qdt.event.UserRegisterEvent;
import plus.qdt.modules.member.entity.dos.User;
import plus.qdt.modules.payment.client.WalletClient;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 会员钱包创建
 *
 * <AUTHOR>
 * @since 2020-07-03 11:20
 */
@Service
@RequiredArgsConstructor
public class UserWalletExecute implements UserRegisterEvent {

    private final WalletClient walletClient;

    @Override
    public void userRegister(User member) {
//         有些情况下，会同时创建一个member_id的两条数据
        walletClient.getMemberWallet(member.getId());
    }
}
