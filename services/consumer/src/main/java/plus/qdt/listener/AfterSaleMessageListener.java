package plus.qdt.listener;

import cn.hutool.json.JSONUtil;
import plus.qdt.event.AfterSaleStatusChangeEvent;
import plus.qdt.modules.order.aftersale.entity.dos.AfterSale;
import plus.qdt.modules.order.aftersale.entity.dto.AfterSaleMessageDTO;
import plus.qdt.routing.AfterSaleRoutingKey;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 售后通知
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AfterSaleMessageListener {

    /**
     * 售后订单状态
     */
    private final List<AfterSaleStatusChangeEvent> afterSaleStatusChangeEvents;

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = ("${lili.amqp.after_sale}" + "_" + AfterSaleRoutingKey.AFTER_SALE_STATUS_CHANGE)),
            exchange = @Exchange(value = "${lili.amqp.after_sale}"),
            key = AfterSaleRoutingKey.AFTER_SALE_STATUS_CHANGE))
    public void onMessage(String afterSaleJson) {
        AfterSaleMessageDTO afterSale = JSONUtil.toBean(afterSaleJson, AfterSaleMessageDTO.class);
        for (AfterSaleStatusChangeEvent afterSaleStatusChangeEvent : afterSaleStatusChangeEvents) {
            try {
                afterSaleStatusChangeEvent.afterSaleStatusChange(afterSale);
            } catch (Exception e) {
                log.error("售后消息消费异常：在{}业务中,{},",
                        JSONUtil.toJsonStr(afterSale),
                        afterSaleStatusChangeEvent.getClass().getName(),
                        e);
            }
        }

    }
}
