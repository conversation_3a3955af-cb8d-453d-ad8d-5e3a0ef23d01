package plus.qdt.modules.payment.serviceimpl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import plus.qdt.modules.file.client.UploadClient;
import plus.qdt.modules.file.entity.dto.FileUploadDTO;
import plus.qdt.modules.payment.entity.dos.WechatBill;
import plus.qdt.modules.payment.entity.dto.WechatBillSearchParams;
import plus.qdt.modules.payment.entity.enums.BillTypeEnum;
import plus.qdt.modules.payment.entity.enums.WechatAccountTypeEnum;
import plus.qdt.modules.payment.entity.enums.WechatBillEnum;
import plus.qdt.modules.payment.mapper.WechatBillMapper;
import plus.qdt.modules.payment.service.WechatBillService;
import plus.qdt.modules.payment.util.EcommerceUtil;
import plus.qdt.mybatis.util.PageUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 分账记录 业务实现
 *
 * <AUTHOR>
 * @since 2020-12-19 09:25
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class WechatBillServiceImpl extends ServiceImpl<WechatBillMapper, WechatBill> implements WechatBillService {


    private final EcommerceUtil ecommerceUtil;

    private final UploadClient uploadClient;


    @Override
    public Page<WechatBill> wechatBillPage(WechatBillSearchParams wechatBillSearchParams) {
        return this.page(PageUtil.initPage(wechatBillSearchParams), wechatBillSearchParams.queryWrapper());
    }

    @Override
    public WechatBill getWechatBill(String id) {
        return this.getById(id);
    }

    @Override
    public void tradebill(DateTime dateTime) {
        JSONObject jsonObject = ecommerceUtil.tradeBill(dateTime.toString("yyyy-MM-dd"));
        String downloadUrl = jsonObject.getStr("download_url");
        if (StrUtil.isNotBlank(downloadUrl) && downloadUrl != null) {
            String context = ecommerceUtil.downloadBill(downloadUrl);
            downloadUrl = uploadFile(WechatBillEnum.TRADE.description(), dateTime, context);

            //保存账单信息
            WechatBill wechatBill = new WechatBill();
            wechatBill.setBillType(BillTypeEnum.TRADE_BILL.name());
            wechatBill.setBillDate(dateTime);
            wechatBill.setDownloadUrl(downloadUrl);
            this.save(wechatBill);
        }
    }

    @Override
    public void fundFlowBill(DateTime dateTime) {
        JSONObject jsonObject = ecommerceUtil.fundFlowBill(dateTime.toString("yyyy-MM-dd"), WechatAccountTypeEnum.BASIC.name());
        String downloadUrl = jsonObject.getStr("download_url");
        if (StrUtil.isNotBlank(downloadUrl) && downloadUrl != null) {
            String context = ecommerceUtil.downloadBill(downloadUrl);
            downloadUrl = uploadFile(WechatBillEnum.FUND_BILL.description() + "-" + WechatAccountTypeEnum.BASIC.name(), dateTime, context);

            //保存账单信息
            WechatBill wechatBill = new WechatBill();
            wechatBill.setBillType(BillTypeEnum.FUNDFLOW_BILL.name());
            wechatBill.setBillDate(dateTime);
            wechatBill.setDownloadUrl(downloadUrl);
            wechatBill.setAccountType(WechatAccountTypeEnum.BASIC.name());
            this.save(wechatBill);
        }

        jsonObject = ecommerceUtil.fundFlowBill(dateTime.toString("yyyy-MM-dd"), WechatAccountTypeEnum.OPERATION.name());
        downloadUrl = jsonObject.getStr("download_url");
        if (StrUtil.isNotBlank(downloadUrl) && downloadUrl != null) {
            String context = ecommerceUtil.downloadBill(downloadUrl);
            downloadUrl = uploadFile(WechatBillEnum.FUND_BILL.description() + "-" + WechatAccountTypeEnum.OPERATION.name(), dateTime, context);

            //保存账单信息
            WechatBill wechatBill = new WechatBill();
            wechatBill.setBillType(BillTypeEnum.FUNDFLOW_BILL.name());
            wechatBill.setBillDate(dateTime);
            wechatBill.setDownloadUrl(downloadUrl);
            wechatBill.setAccountType(WechatAccountTypeEnum.OPERATION.name());
            this.save(wechatBill);
        }

        jsonObject = ecommerceUtil.fundFlowBill(dateTime.toString("yyyy-MM-dd"), WechatAccountTypeEnum.FEES.name());
        downloadUrl = jsonObject.getStr("download_url");
        if (StrUtil.isNotBlank(downloadUrl) && downloadUrl != null) {
            String context = ecommerceUtil.downloadBill(downloadUrl);
            downloadUrl = uploadFile(WechatBillEnum.FUND_BILL.description() + "-" + WechatAccountTypeEnum.FEES.name(), dateTime, context);

            //保存账单信息
            WechatBill wechatBill = new WechatBill();
            wechatBill.setBillType(BillTypeEnum.FUNDFLOW_BILL.name());
            wechatBill.setBillDate(dateTime);
            wechatBill.setDownloadUrl(downloadUrl);
            wechatBill.setAccountType(WechatAccountTypeEnum.FEES.name());
            this.save(wechatBill);
        }


    }

    @Override
    public void bills(DateTime dateTime) {
        JSONObject jsonObject = ecommerceUtil.bills(dateTime.toString("yyyy-MM-dd"));
        String downloadUrl = jsonObject.getStr("download_url");
        if (StrUtil.isNotBlank(downloadUrl) && downloadUrl != null) {
            String context = ecommerceUtil.downloadBill(downloadUrl);
            downloadUrl = uploadFile(WechatBillEnum.PROFITSHARING.description(), dateTime, context);

            //保存账单信息
            WechatBill wechatBill = new WechatBill();
            wechatBill.setBillType(BillTypeEnum.PROFITSHARING.name());
            wechatBill.setBillDate(dateTime);
            wechatBill.setDownloadUrl(downloadUrl);
            this.save(wechatBill);
        }

    }

    @Override
    public void ecommerceFundFlowBill(DateTime dateTime) {
        JSONObject jsonObject = ecommerceUtil.ecommerceFundFlowBill(dateTime.toString("yyyy-MM-dd"));
        String downloadUrl = jsonObject.getStr("download_url");
        if (StrUtil.isNotBlank(downloadUrl) && downloadUrl != null) {
            String context = ecommerceUtil.downloadBill(downloadUrl);
            downloadUrl = uploadFile(WechatBillEnum.FUND_FLOW_BILL.description(), dateTime, context);

            //保存账单信息
            WechatBill wechatBill = new WechatBill();
            wechatBill.setBillType(WechatBillEnum.FUND_FLOW_BILL.name());
            wechatBill.setBillDate(dateTime);
            wechatBill.setDownloadUrl(downloadUrl);
            this.save(wechatBill);
        }
    }

    /**
     * 上传文件
     *
     * @param fileName
     * @param dateTime
     * @param context
     * @return
     */
    private String uploadFile(String fileName, DateTime dateTime, String context) {
        FileUploadDTO fileUploadDTO = new FileUploadDTO();
        fileUploadDTO.setBucketName("kashi-shop-wechat");
        fileUploadDTO.setKey(fileName + dateTime.toString("yyyy-MM-dd") + ".xlsx");
        fileUploadDTO.setContext(context);
        return uploadClient.upload(fileUploadDTO);
    }

}
