package plus.qdt.modules.kit.plugin.wallet;

import cn.hutool.core.text.CharSequenceUtil;
import plus.qdt.common.enums.ClientTypeEnum;
import plus.qdt.common.enums.ResultCode;
import plus.qdt.common.event.TransactionCommitSendMQEvent;
import plus.qdt.common.exception.ServiceException;
import plus.qdt.common.security.context.UserContext;
import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.utils.SnowFlake;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.exchange.AmqpExchangeProperties;
import plus.qdt.modules.kit.Payment;
import plus.qdt.modules.payment.entity.dos.*;
import plus.qdt.modules.payment.entity.dto.*;
import plus.qdt.modules.payment.entity.enums.OutOrderLogStatusEnums;
import plus.qdt.modules.payment.entity.enums.PaymentMethodEnum;
import plus.qdt.modules.payment.entity.enums.PlatformWalletEnum;
import plus.qdt.modules.payment.entity.enums.WalletServiceTypeEnum;
import plus.qdt.modules.payment.entity.vo.OutOrderLogVO;
import plus.qdt.modules.payment.entity.vo.WalletVO;
import plus.qdt.modules.payment.service.OutOrderDetailLogService;
import plus.qdt.modules.payment.service.OutOrderLogService;
import plus.qdt.modules.payment.service.PaymentLogService;
import plus.qdt.modules.payment.service.PaymentSubsidiesService;
import plus.qdt.modules.wallet.service.WalletService;
import plus.qdt.routing.PaymentRoutingKey;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * WalletPlugin
 *
 * <AUTHOR>
 * @version v1.0 2021-02-20 10:14
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WalletPlugin implements Payment {


    private final ApplicationEventPublisher applicationEventPublisher;

    private final AmqpExchangeProperties amqpExchangeProperties;

    private final WalletService walletService;



    private final PaymentLogService paymentLogService;

    private final OutOrderLogService outOrderLogService;

    private final OutOrderDetailLogService outOrderDetailLogService;

    private final PaymentSubsidiesService paymentSubsidiesService;

    @Override
    public ResultMessage<Object> h5pay(PaymentWakeupParam paymentWakeupParam) {

        return exec(paymentWakeupParam);
    }

    @Override
    public ResultMessage<Object> jsApiPay(PaymentWakeupParam paymentWakeupParam) {

        return exec(paymentWakeupParam);
    }

    @Override
    public ResultMessage<Object> appPay(PaymentWakeupParam paymentWakeupParam) {

        return exec(paymentWakeupParam);
    }

    @Override
    public ResultMessage<Object> nativePay(PaymentWakeupParam paymentWakeupParam) {

        return exec(paymentWakeupParam);
    }

    @Override
    public ResultMessage<Object> mpPay(PaymentWakeupParam paymentWakeupParam) {

        return exec(paymentWakeupParam);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PaymentSubsidies subsidies(SubsidiesParams subsidiesParams) {

        WalletVO walletVO = walletService.getWalletVO(subsidiesParams.getPayerId());

        //如果订单金额大于钱包金额，直接返回 .当id为-1时，代表平台付款，不需要判断余额
        if (!subsidiesParams.getPayerId().equals("-1") && walletVO.getBalance() < subsidiesParams.getPrice()) {
            throw new ServiceException(ResultCode.WALLET_INSUFFICIENT);
        }


        //支付发起交易号
        try {
            PaymentSubsidies paymentSubsidies = PaymentSubsidies.builder()
                    //根据订单号，生成规律的补差流水号
                    .orderSn(subsidiesParams.getOrderSn())
                    .nickname(subsidiesParams.getNickname())
                    .payerId(subsidiesParams.getPayerId())
                    .description(PaymentSubsidies.generateSubsidiesText(subsidiesParams.getOrderSn(),
                            subsidiesParams.getPrice()))
                    .paymentClient(ClientTypeEnum.PC.name())
                    .paymentMethod(PaymentMethodEnum.WALLET.name())
                    .price(subsidiesParams.getPrice())
                    .payeeId("-1")
                    .createTime(new Date())
                    .outTradeNo(SnowFlake.getIdStr())
                    .transactionId(SnowFlake.getIdStr())
                    .build();


            //支付成功后，扣除钱包金额
            walletService.reduce(
                    UserWalletUpdateDTO.builder()
                            .userId(subsidiesParams.getPayerId())
                            .orderSn(subsidiesParams.getOrderSn())
                            .amount(subsidiesParams.getPrice())
                            .detail("订单[" + paymentSubsidies.getOrderSn() + "]补差,金额[" + paymentSubsidies.getPrice() + "]")
                            .transactionId(paymentSubsidies.getTransactionId())
                            .outTradeNo(paymentSubsidies.getOutTradeNo())
                            .serviceType(WalletServiceTypeEnum.PAY)
                            .platformWalletId(PlatformWalletEnum.BALANCE.getWalletUserId())
                            .build());

            PaymentCallback paymentCallback =
                    PaymentCallback.builder()
                            .paymentSubsidies(paymentSubsidies)
                            .isCombine(false)
                            .build();

            applicationEventPublisher.publishEvent(
                    TransactionCommitSendMQEvent.builder()
                            .source("补差支付回调")
                            .exchange(amqpExchangeProperties.getPayment())
                            .routingKey(PaymentRoutingKey.SUBSIDIES_CALLBACK)
                            .message(paymentCallback)
                            .build()
            );
            paymentSubsidiesService.save(paymentSubsidies);
            return paymentSubsidies;
        } catch (Exception e) {
            log.error("补差支付失败", e);
            throw new ServiceException(ResultCode.PAY_ERROR);
        }


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OutOrderLogVO profitSharing(ProfitSharingRequestDTO profitSharingRequestDTO) {
        //获取支付日志
        PaymentLog paymentLog =
                paymentLogService.getPaymentLogByOrderSN(profitSharingRequestDTO.getOrderSn());
        if (paymentLog == null) {
            throw new ServiceException(ResultCode.PROFIT_SHARING_ERROR, "支付日志不存在");
        }

        OutOrderLog outOrderLog = OutOrderLog.builder().paymentMethod(profitSharingRequestDTO.getPaymentMethodEnum().name())
                .orderSn(profitSharingRequestDTO.getOrderSn())
                .amount(profitSharingRequestDTO.getTotalAmount())
                .paymentMethod(PaymentMethodEnum.WALLET.name())
                .status(OutOrderLogStatusEnums.FINISHED.name())
                .outOrderNo(SnowFlake.createStr("OOL"))
                .build();

        outOrderLogService.save(outOrderLog);

        //保存分账子单记录
        List<OutOrderDetailLog> outOrderDetailLogs = new ArrayList<>();
        for (OutOrderItem outOrderItem : profitSharingRequestDTO.getOutOrderItems()) {
            OutOrderDetailLog outOrderDetailLog = new OutOrderDetailLog(profitSharingRequestDTO.getOrderSn(), outOrderLog.getOutOrderNo(),
                    outOrderItem);
            outOrderDetailLogs.add(outOrderDetailLog);
        }

        //保存分账子账单信息
        outOrderDetailLogService.saveBatch(outOrderDetailLogs);

        walletService.profitSharing(profitSharingRequestDTO);

        return new OutOrderLogVO(outOrderLog, outOrderDetailLogs);
    }

    @Override
    @Transactional
    public void profitSharingSubsidies(OutOrderLog outOrderLog) {

        List<PaymentSubsidies> paymentSubsidies = paymentSubsidiesService.listByOrderSn(outOrderLog.getOrderSn());

        if (paymentSubsidies.isEmpty()) {
            return;
        }

        //将出账单号更新近日志信息
        paymentSubsidies.forEach(item -> {
            item.setOutOrderNo(outOrderLog.getOutOrderNo());
        });
        paymentSubsidiesService.updateBatchById(paymentSubsidies);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OutOrderLogVO specialProfitSharing(OutOrderLog outOrderLog, ProfitSharingRequestDTO profitSharingRequestDTO) {

        //保存分账子单记录
        List<OutOrderDetailLog> outOrderDetailLogs = new ArrayList<>();
        for (OutOrderItem outOrderItem : profitSharingRequestDTO.getOutOrderItems()) {
            //只对有金额的子单进行分账 小于0的在下单审批过程中已经通过钱包补交了。
            if (outOrderItem.getPrice() > 0) {
                OutOrderDetailLog outOrderDetailLog = new OutOrderDetailLog(profitSharingRequestDTO.getOrderSn(), outOrderLog.getOutOrderNo(),
                        outOrderItem);
                outOrderDetailLogs.add(outOrderDetailLog);
            }
        }

        //保存分账子账单信息
        outOrderDetailLogService.saveBatch(outOrderDetailLogs);

        walletService.profitSharing(profitSharingRequestDTO);

        return new OutOrderLogVO(outOrderLog, outOrderDetailLogs);
    }

    @Override
    public OutOrderLog profitSharingPlatform(PaymentLog paymentLog) {

        OutOrderLog outOrderLog = OutOrderLog.builder().paymentMethod(paymentLog.getPaymentMethod())
                .orderSn(paymentLog.getOrderSn())
                .amount(paymentLog.getPrice())
                .paymentMethod(PaymentMethodEnum.WALLET.name())
                .status(OutOrderLogStatusEnums.FINISHED.name())
                .outOrderNo(SnowFlake.createStr("OOL"))
                .build();

        outOrderLogService.save(outOrderLog);

        //保存分账子单记录
        OutOrderDetailLog outOrderDetailLog = OutOrderDetailLog.builder()
                .orderSn(paymentLog.getOrderSn())
                .outOrderNo(outOrderLog.getOutOrderNo())
                .amount(paymentLog.getPrice())
                .subMchid(PlatformWalletEnum.BALANCE.getWalletUserId())
                .result("SUCCESS")
                .subMchName(PlatformWalletEnum.BALANCE.getDescription())
                .description(OutOrderDetailLog.generateSubsidiesToPlatform(paymentLog.getOrderSn(), paymentLog.getPrice()))
                .build();
        //保存分账子账单信息
        outOrderDetailLogService.save(outOrderDetailLog);
        return outOrderLog;
    }

    /**
     * 保存支付日志
     *
     * @param paymentWakeupParam 支付参数
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultMessage<Object> exec(PaymentWakeupParam paymentWakeupParam) {

        WalletVO walletVO = walletService.getWalletVO(UserContext.getCurrentId());

        PayParam payParam = paymentWakeupParam.getPayParam();
        //如果订单金额大于钱包金额，直接返回
        if (walletVO.getBalance() < payParam.getTotalAmount()) {
            throw new ServiceException(ResultCode.WALLET_INSUFFICIENT);
        }

        //支付发起交易号
        try {
            if (Boolean.TRUE.equals(paymentWakeupParam.getIsCombine())) {
                CombinePaymentLog combinePaymentLog = paymentWakeupParam.getCombinePaymentLog();
                combinePaymentLog.setCombineOutTradeNo(SnowFlake.getIdStr());
                //标记已支付，且已校验
                combinePaymentLog.setIsCheck(true);

                paymentWakeupParam.getPaymentLogs().forEach(paymentLog -> {
                    paymentLog.setOutTradeNo(SnowFlake.getIdStr());
                    paymentLog.setTransactionId(SnowFlake.getIdStr());
                    paymentLog.setCombineOutTradeNo(combinePaymentLog.getCombineOutTradeNo());
                    //标记已支付，且已校验
                    paymentLog.setIsPay(true);
                    paymentLog.setIsCheck(true);
                });


                //扣除钱包金额
                walletService.reduce(
                        UserWalletUpdateDTO.builder()
                                .userId(UserContext.getCurrentId())
                                .orderSn(paymentWakeupParam.getCombinePaymentLog().getOrderSn())
                                .scene(UserContext.getCurrentUser().getScene())
                                .amount(payParam.getTotalAmount())
                                .detail("合并支付，订单号：[" + paymentWakeupParam.getPaymentLogs().stream().map(PaymentLog::getOrderSn).collect(Collectors.joining(", ")) + "]，支付金额[" + payParam.getTotalAmount() + "]")
                                .transactionId("合单支付")
                                .outTradeNo(combinePaymentLog.getCombineOutTradeNo())
                                .serviceType(WalletServiceTypeEnum.PAY)
                                .platformWalletId(PlatformWalletEnum.BALANCE.getWalletUserId())
                                .build());
            } else {

                PaymentLog paymentLog = paymentWakeupParam.getPaymentLog();

                paymentLog.setOutTradeNo(SnowFlake.getIdStr());
                paymentLog.setTransactionId(SnowFlake.getIdStr());
                //标记已支付，且已校验
                paymentLog.setIsPay(true);
                paymentLog.setIsCheck(true);


                //支付成功后，扣除钱包金额
                walletService.reduce(
                        UserWalletUpdateDTO.builder()
                                .userId(UserContext.getCurrentId())
                                .orderSn(paymentLog.getOrderSn())
                                .scene(UserContext.getCurrentUser().getScene())
                                .amount(payParam.getTotalAmount())
                                .detail("支付订单[" + paymentLog.getOrderSn() + "]金额[" + paymentLog.getPrice() + "]")
                                .transactionId(paymentLog.getTransactionId())
                                .outTradeNo(paymentLog.getOutTradeNo())
                                .platformWalletId(PlatformWalletEnum.BALANCE.getWalletUserId())
                                .serviceType(WalletServiceTypeEnum.PAY)
                                .build());
            }

            PaymentCallback paymentCallback =
                    Boolean.TRUE.equals(payParam.getIsCombine()) ?
                            PaymentCallback.builder()
                                    .paymentLogs(paymentWakeupParam.getPaymentLogs())
                                    .combinePaymentLog(paymentWakeupParam.getCombinePaymentLog())
                                    .isCombine(true)
                                    .build() :
                            PaymentCallback.builder()
                                    .paymentLog(paymentWakeupParam.getPaymentLog())
                                    .isCombine(false)
                                    .build();

            applicationEventPublisher.publishEvent(
                    TransactionCommitSendMQEvent.builder()
                            .source("余额支付回调")
                            .exchange(amqpExchangeProperties.getPayment())
                            .routingKey(PaymentRoutingKey.PAYMENT_CALLBACK)
                            .message(paymentCallback)
                            .build()
            );
        } catch (Exception e) {
            log.error("余额支付失败", e);
            throw new ServiceException(ResultCode.PAY_ERROR);
        }

        return ResultUtil.success();
    }

    @Override
    public void refund(RefundLog refundLog) {
        PaymentLog paymentLog = paymentLogService.getPaymentLogByOutTradeNo(refundLog.getOutTradeNo());
        if (paymentLog == null) {
            refundLog.setErrorMessage("支付日志不存在，无法退款");
        } else {
            refundLog.setIsRefund(true);
            refundLog.setRefundTransactionId(SnowFlake.getIdStr());
            String detail;
            if (CharSequenceUtil.isNotEmpty(refundLog.getAfterSaleNo())) {
                detail = "订单[" + refundLog.getOrderSn() + "]，售后单[" + refundLog.getAfterSaleNo() + "]，退还金额[" + refundLog.getPrice() + "]";
            } else {
                detail = "订单[" + refundLog.getOrderSn() + "]，退还金额[" + refundLog.getPrice() + "]";
            }

            walletService.increase(
                    UserWalletUpdateDTO.builder()
                            .userId(refundLog.getUserId())
                            .amount(refundLog.getPrice())
                            .orderSn(refundLog.getOrderSn())
                            .detail(detail)
                            .outTradeNo(refundLog.getAfterSaleNo())
                            .platformWalletId(PlatformWalletEnum.BALANCE.getWalletUserId())
                            .transactionId(refundLog.getTransactionId())
                            .serviceType(WalletServiceTypeEnum.REFUND)
                            .build());

        }
    }


}



