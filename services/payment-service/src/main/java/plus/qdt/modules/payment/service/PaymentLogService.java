package plus.qdt.modules.payment.service;

import plus.qdt.modules.order.order.entity.dto.OrderSearchParams;
import plus.qdt.modules.payment.entity.dos.PaymentLog;
import plus.qdt.modules.payment.entity.dto.ProfitSharingRequestDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;


/**
 * 支付唤起日志业务层
 *
 * <AUTHOR> @ gmail.com)
 * @version v4.0
 * @Description:
 * @since 2023/3/28 14:33
 */

public interface PaymentLogService extends IService<PaymentLog> {


    /**
     * 获取支付唤起日志
     *
     * @param payOrderNo 支付单号
     * @return
     */
    PaymentLog getPaymentlog(String payOrderNo);


    /**
     * 根据订单号获取支付日志
     *
     * @param sn 订单号
     * @return 支付结果
     */
    Boolean getPaymentResult(String sn);

    /**
     * 分页获取支付日志
     *
     * @param searchParams 查询参数
     * @return 支付日志
     */
    Page<PaymentLog> queryPaymentLogs(OrderSearchParams searchParams);


    /**
     * 根据合单支付编号查询支付日志
     *
     * @param combineOutTradeNo 合单支付编号
     * @return 支付日志
     */
    List<PaymentLog> getPaymentLogByCombineOutTradeNo(String combineOutTradeNo);

    /**
     * 根据支付单号查询支付日志
     *
     * @param outTradeNo 支付单号
     * @return 支付日志
     */
    PaymentLog getPaymentLogByOutTradeNo(String outTradeNo);

    /**
     * 根据订单号查询支付日志
     *
     * @param orderSn 订单号
     * @return 支付日志
     */
    PaymentLog getPaymentLogByOrderSN(String orderSn);


    /**
     * 生成0元订单的支付流水，用于分账逻辑
     *
     * @param profitSharingRequestDTO 分账请求参数
     * @return 支付日志
     */
    PaymentLog generateZeroLog(ProfitSharingRequestDTO profitSharingRequestDTO);
}