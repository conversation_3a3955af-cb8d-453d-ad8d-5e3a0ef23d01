package plus.qdt.modules.payment.service;


import plus.qdt.modules.payment.entity.dos.WithdrawLog;
import plus.qdt.modules.payment.entity.dto.WithdrawLogSearchParams;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 预约提现记录业务层
 *
 * <AUTHOR>
 * @since 2022/10/19
 **/
public interface WithdrawLogService extends IService<WithdrawLog> {

    /**
     * 提现记录分页
     *
     * @param withdrawLogSearchParams 分页VO
     * @return
     */
    IPage<WithdrawLog> withdrawLogPage(WithdrawLogSearchParams withdrawLogSearchParams);

    /**
     * 根据ID获取提现记录
     *
     * @param id ID
     * @return
     */
    WithdrawLog getWithdrawLog(String id);

    /**
     * 二级商户提现
     */
    void subWithdraw(String subMchid);

    /**
     * 所有商户提现
     */
    void subWithdraw();

    /**
     * 同步提现记录状态
     */
    void querySubWithdraw();

}
