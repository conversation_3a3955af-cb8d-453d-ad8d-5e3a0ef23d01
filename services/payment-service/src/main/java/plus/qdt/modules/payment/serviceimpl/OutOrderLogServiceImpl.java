package plus.qdt.modules.payment.serviceimpl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import plus.qdt.modules.payment.entity.dos.OutOrderDetailLog;
import plus.qdt.modules.payment.entity.dos.OutOrderLog;
import plus.qdt.modules.payment.entity.dto.OutOrderLogSearchParams;
import plus.qdt.modules.payment.entity.enums.OutOrderLogStatusEnums;
import plus.qdt.modules.payment.entity.vo.OutOrderDetailVO;
import plus.qdt.modules.payment.mapper.OutOrderLogMapper;
import plus.qdt.modules.payment.service.OutOrderDetailLogService;
import plus.qdt.modules.payment.service.OutOrderLogService;
import plus.qdt.modules.payment.service.PaymentSubsidiesService;
import plus.qdt.modules.payment.util.EcommerceUtil;
import plus.qdt.modules.payment.wechat.model.profitsharing.ProfitSharingResponse;
import plus.qdt.modules.payment.wechat.model.profitsharing.ReceiverResponse;
import plus.qdt.mybatis.util.PageUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 分账记录 业务实现
 *
 * <AUTHOR>
 * @since 2020-12-19 09:25
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class OutOrderLogServiceImpl extends ServiceImpl<OutOrderLogMapper, OutOrderLog> implements OutOrderLogService {

    private final EcommerceUtil ecommerceUtil;

    private final OutOrderDetailLogService outOrderDetailLogService;

    private final PaymentSubsidiesService paymentSubsidiesService;

    @Override
    public IPage<OutOrderLog> outOrderLogPage(OutOrderLogSearchParams outOrderLogSearchParams) {
        return this.page(PageUtil.initPage(outOrderLogSearchParams), outOrderLogSearchParams.queryWrapper());
    }

    @Override
    public OutOrderLog getOutOrderLog(String id) {
        return this.getById(id);
    }

    @Override
    public OutOrderLog geOutOrderLogByOutOrderNo(String outOrderNo) {
        return this.getOne(new LambdaQueryWrapper<OutOrderLog>().eq(OutOrderLog::getOutOrderNo, outOrderNo));
    }

    @Override
    public void queryProfitSharing() {
        List<OutOrderLog> outOrderLogList = this.list(new LambdaQueryWrapper<OutOrderLog>()
                .eq(OutOrderLog::getStatus, OutOrderLogStatusEnums.PROCESSING.name()));
        for (OutOrderLog outOrderLog : outOrderLogList) {
            JSONObject jsonObject = ecommerceUtil.queryProfitSharing(outOrderLog.getSubMchid(), outOrderLog.getTransactionId(),
                    outOrderLog.getOutOrderNo());
            ProfitSharingResponse profitSharingResponse = JSONUtil.toBean(jsonObject, ProfitSharingResponse.class);
            //修改分账单状态
            this.update(new LambdaUpdateWrapper<OutOrderLog>()
                    .eq(OutOrderLog::getOutOrderNo, outOrderLog.getOutOrderNo())
                    .set(OutOrderLog::getStatus, profitSharingResponse.getStatus()));

            for (ReceiverResponse receiverResponse : profitSharingResponse.getReceivers()) {
                //修改分账单子单状态
                LambdaUpdateWrapper lambdaUpdateWrapper = new LambdaUpdateWrapper<OutOrderDetailLog>()
                        .eq(OutOrderDetailLog::getDetailId, receiverResponse.getDetail_id())
                        .set(OutOrderDetailLog::getResult, receiverResponse.getResult())
//                        .set(receiverResponse.getFinish_time() != null, OutOrderDetailLog::getFinishTime, receiverResponse.getFinish_time())
                        .set(receiverResponse.getFail_reason() != null, OutOrderDetailLog::getFailReason, receiverResponse.getFail_reason());
                outOrderDetailLogService.update(lambdaUpdateWrapper);
            }
        }

    }

    @Override
    public List<OutOrderDetailLog> outOrderDetailLogList(String outOrderNo) {
        return outOrderDetailLogService.list(new LambdaQueryWrapper<OutOrderDetailLog>()
                .eq(OutOrderDetailLog::getOutOrderNo, outOrderNo));
    }

    @Override
    public OutOrderDetailVO getDetailVO(String outOrderNo) {
        OutOrderDetailVO outOrderDetailVO = new OutOrderDetailVO();

        outOrderDetailVO.setOutOrderDetailLogs(this.outOrderDetailLogList(outOrderNo));
        outOrderDetailVO.setPaymentSubsidies(paymentSubsidiesService.listByOutOrderNO(outOrderNo));

        return outOrderDetailVO;
    }

}
