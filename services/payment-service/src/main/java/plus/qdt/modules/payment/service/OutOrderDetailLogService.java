package plus.qdt.modules.payment.service;

import plus.qdt.modules.payment.entity.dos.OutOrderDetailLog;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 分账记录业务层
 *
 * <AUTHOR>
 * @since 2022/10/19
 **/
public interface OutOrderDetailLogService extends IService<OutOrderDetailLog> {

    /**
     * 根据分账单号获取分账记录
     * @param outOrderNo 分账单号
     * @return 分账记录
     */
    List<OutOrderDetailLog> getByOutOrderNo(String outOrderNo);

}
