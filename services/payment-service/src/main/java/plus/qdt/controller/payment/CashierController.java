package plus.qdt.controller.payment;

import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.kit.CashierSupport;
import plus.qdt.modules.payment.entity.enums.PaymentMethodEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 买家端,收银台接口
 *
 * <AUTHOR>
 * @since 2020-12-18 16:59
 */
@Slf4j
@RestController
@Tag(name = "买家端,收银台接口")
@RequestMapping("/payment/cashier")
@RequiredArgsConstructor
public class CashierController {

    private final CashierSupport cashierSupport;

    @Operation(summary = "支付回调")
    @RequestMapping(value = "/callback/{paymentMethod}", method = {RequestMethod.GET, RequestMethod.POST})
    public ResultMessage<Object> callback(@PathVariable String paymentMethod) {

        PaymentMethodEnum paymentMethodEnum = PaymentMethodEnum.valueOf(paymentMethod);

        cashierSupport.callback(paymentMethodEnum);

        return ResultUtil.success();
    }

    @Operation(summary = "支付异步通知")
    @RequestMapping(value = "/notify/{paymentMethod}", method = {RequestMethod.GET, RequestMethod.POST})
    public void notify( @PathVariable String paymentMethod) {

        PaymentMethodEnum paymentMethodEnum = PaymentMethodEnum.valueOf(paymentMethod);

        cashierSupport.notify(paymentMethodEnum );
    }

    @Operation(summary = "查询支付结果")
    @GetMapping(value = "/result")
    public ResultMessage<Boolean> paymentResult(String sn) {
        return ResultUtil.data(cashierSupport.paymentResult(sn));
    }
}
