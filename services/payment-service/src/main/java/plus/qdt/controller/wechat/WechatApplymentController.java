package plus.qdt.controller.wechat;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import plus.qdt.common.context.ThreadContextHolder;
import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.goods.entity.dto.GoodsOperationDTO;
import plus.qdt.modules.payment.entity.dto.BankData;
import plus.qdt.modules.payment.entity.dto.Branches;
import plus.qdt.modules.payment.entity.dto.Provinces;
import plus.qdt.modules.payment.entity.dto.WechatApplymentSearchParams;
import plus.qdt.modules.payment.entity.vo.WechatApplymentVO;
import plus.qdt.modules.payment.service.WechatApplymentService;
import plus.qdt.modules.payment.service.WechatBankService;
import plus.qdt.modules.payment.service.WechatBillService;
import plus.qdt.modules.payment.wechat.applyments.EcommerceDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

/**
 * 微信进件
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "管理端,微信进件接口")
@RequestMapping("/payment/wechatApplyment")
public class WechatApplymentController {

    @Autowired
    private WechatApplymentService wechatApplymentService;

    @Autowired
    private WechatBankService wechatBankService;
    @Autowired
    private WechatBillService wechatBillService;

    @Operation(summary = "获取微信进件分页")
    @GetMapping
    public ResultMessage<IPage<WechatApplymentVO>> getByPage(WechatApplymentSearchParams wechatApplymentSearchParams) {
        return ResultUtil.data(wechatApplymentService.applymentPage(wechatApplymentSearchParams));
    }

    @Operation(summary = "通过业务单号获取微信进件")
    @GetMapping(value = "/get/{outRequestNo}")
    public ResultMessage<EcommerceDTO> get(@PathVariable String outRequestNo) {
        return ResultUtil.data(wechatApplymentService.getEcommerceDTO(outRequestNo));
    }

    @Operation(summary = "新增微信进件")
    @PostMapping(value = "/create")
    public ResultMessage<GoodsOperationDTO> create(@RequestBody EcommerceDTO ecommerceDTO) {
        wechatApplymentService.applyment(ecommerceDTO);
        return ResultUtil.success();
    }

    @Operation(summary = "暂存微信进件")
    @PostMapping(value = "/save")
    public ResultMessage<GoodsOperationDTO> save(@RequestBody EcommerceDTO ecommerceDTO) {
        wechatApplymentService.addApplyment(ecommerceDTO);
        return ResultUtil.success();
    }

    @Operation(summary = "修改微信进件")
    @PutMapping(value = "/update/{id}")
    public ResultMessage<GoodsOperationDTO> update(@PathVariable String id, @RequestBody EcommerceDTO ecommerceDTO) {
        wechatApplymentService.editApplyment(id, ecommerceDTO);
        return ResultUtil.success();
    }

    @Operation(summary = "同步微信进件状态")
    @PostMapping(value = "/applyments/{id}")
    public ResultMessage<Object> update(@PathVariable String id) {
        wechatApplymentService.applyments(id);
        return ResultUtil.success();
    }


    @Operation(summary = "获取对公银行")
    @GetMapping(value = "/getBankInfo")
    public ResultMessage<List<BankData>> getBankInfo() {
        return ResultUtil.data(wechatBankService.corporateBankList());
    }

    @Operation(summary = "获取对私银行")
    @GetMapping(value = "/getPersonalBank")
    public ResultMessage<List<BankData>> getPersonBankInfo() {
        return ResultUtil.data(wechatBankService.personalBankList());
    }

    @Operation(summary = "获取地区信息")
    @GetMapping(value = "/provinces")
    public ResultMessage<List<Provinces>> getProvinces() {
        return ResultUtil.data(wechatBankService.getProvinces());
    }


    @Operation(summary = "获取支行列表")
    @GetMapping(value = "/branches")
    public ResultMessage<List<Branches>> branches(String bankAliasCode, String cityCode) {
        return ResultUtil.data(wechatBankService.branches(bankAliasCode, cityCode));
    }


    @Operation(summary = "根据商户号获取余额")
    @GetMapping(value = "/subNowBalance/{subMchid}")
    public ResultMessage<Object> subNowBalance(@PathVariable String subMchid) {
        wechatApplymentService.subNowBalance(subMchid);
        return ResultUtil.success();
    }

    @Operation(summary = "下载列表", responses = @ApiResponse(content = {
            @io.swagger.v3.oas.annotations.media.Content(mediaType = "application/octet-stream")
    }))
    @GetMapping(value = "/downLoadExcel")
    public ResultMessage<Object> downLoadExcel(@PathVariable String subMchid) {
        HttpServletResponse response = ThreadContextHolder.getHttpResponse();
        wechatApplymentService.export(response);
        return ResultUtil.success();
    }

    @Operation(summary = "导入个人商户")
    @PostMapping(value = "/readFile")
    public ResultMessage<Object> readFile(@RequestPart("files") MultipartFile files) {
        wechatApplymentService.readFile(files);
        return ResultUtil.success();
    }

    @Operation(summary = "申请交易账单API---今日")
    @PutMapping("/tradebill")
    public void tradebill() {
        log.info("执行每小时定时器");

        log.info("当前小时:" + DateUtil.hour(new DateTime(), true));

        for (int i = -1; i > -8; i--) {
            wechatBillService.tradebill(DateUtil.offsetDay(new Date(), i));
            wechatBillService.fundFlowBill(DateUtil.offsetDay(new Date(), i));
            wechatBillService.bills(DateUtil.offsetDay(new Date(), i));
        }
    }


//    @Autowired
//    private RefundLogService refundLogService;
//    @Autowired
//    private PaymentLogService paymentLogService;
//    @PutMapping("/refundAccount")
//    @Operation(summary = "退款单对账")
//    public ResultMessage<Object> refundAccount() {
//        refundLogService.account();
//        return ResultUtil.success();
//    }
//
//    @PutMapping("/account")
//    @Operation(summary = "付款单对账")
//    public ResultMessage<Object> account() {
//        paymentLogService.account();
//        return ResultUtil.success();
//    }
}
