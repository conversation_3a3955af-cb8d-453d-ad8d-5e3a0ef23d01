server:
  #端口号
  port: 11116
spring:
  #服务名称
  application:
    name: '@artifactId@'
  #运行环境
  profiles:
    active: '@profiles.active@'
  cloud:
    nacos:
      discovery:
        server-addr: '@nacos.server@'
        namespace: '@nacos.namespaceId@'
        group: '@nacos.group@'
      username: '@nacos.username@'
      password: '@nacos.password@'

      config:
        server-addr: '@nacos.server@'
        username: '@nacos.username@'
        password: '@nacos.password@'
        namespace: '@nacos.namespaceId@'
        group: '@nacos.group@'
        file-extension: yml
        shared-configs:
          - data-id: <EMAIL>@.${spring.cloud.nacos.config.file-extension} # 配置文件名-Data Id
            group: @nacos.group@  # 默认为DEFAULT_GROUP
            refresh: true   # 是否动态刷新，默认为false
seata:
  application-id: @artifactId@
  tx-service-group: @artifactId@-group
