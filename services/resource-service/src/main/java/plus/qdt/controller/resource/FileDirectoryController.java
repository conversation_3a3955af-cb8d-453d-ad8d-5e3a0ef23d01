package plus.qdt.controller.resource;

import plus.qdt.common.security.AuthUser;
import plus.qdt.common.security.context.UserContext;
import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.file.entity.FileDirectory;
import plus.qdt.modules.file.entity.dto.FileDirectoryDTO;
import plus.qdt.modules.file.service.FileDirectoryService;
import plus.qdt.modules.file.service.FileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 文件目录管理接口
 *
 * <AUTHOR>
 * @since 2020/11/26 15:41
 */
@RestController
@Tag(name = "文件目录管理接口")
@RequestMapping("/resource/file/directory")
@RequiredArgsConstructor
public class FileDirectoryController {

    private final FileDirectoryService fileDirectoryService;
    private final FileService fileService;

    @Operation(summary = "获取文件目录列表")
    @GetMapping
    public ResultMessage<List<FileDirectoryDTO>> getSceneFileList() {
        return ResultUtil.data(fileDirectoryService.getFileDirectoryList(UserContext.getCurrentUser().getId()));
    }

    @Operation(summary = "添加文件目录")
    @PostMapping
    public ResultMessage<FileDirectory> addSceneFileList(@RequestBody FileDirectory fileDirectory) {
        AuthUser currentUser = UserContext.getCurrentUser();
        fileDirectory.setDirectoryType(currentUser.getScene().name());
        fileDirectory.setOwnerId(currentUser.getId());
        // 增加参数验证处理
        fileDirectory.validateParams();
        fileDirectoryService.save(fileDirectory);
        return ResultUtil.data(fileDirectory);
    }

    @Operation(summary = "修改文件目录")
    @PutMapping
    public ResultMessage<FileDirectory> editSceneFileList(@RequestBody FileDirectory fileDirectory) {
        fileDirectory.setDirectoryType(UserContext.getCurrentUser().getScene().name());
        fileDirectoryService.updateById(fileDirectory);
        return ResultUtil.data(fileDirectory);
    }

    @Operation(summary = "删除文件目录")
    @DeleteMapping("/{id}")
    public ResultMessage<Object> deleteSceneFileList(@PathVariable String id) {
        //删除文件夹下面的图片
        fileService.batchDeleteByDirectory(id);
        //删除目录
        fileDirectoryService.removeById(id);
        return ResultUtil.success();
    }

}
