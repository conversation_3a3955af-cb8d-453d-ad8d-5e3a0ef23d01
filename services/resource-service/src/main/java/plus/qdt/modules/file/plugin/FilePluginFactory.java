package plus.qdt.modules.file.plugin;

import cn.hutool.json.JSONUtil;
import plus.qdt.common.exception.ServiceException;
import plus.qdt.modules.file.entity.enums.OssEnum;
import plus.qdt.modules.file.plugin.impl.AliFilePlugin;
import plus.qdt.modules.file.plugin.impl.HuaweiFilePlugin;
import plus.qdt.modules.file.plugin.impl.MinioFilePlugin;
import plus.qdt.modules.file.plugin.impl.TencentFilePlugin;
import plus.qdt.modules.system.client.SettingClient;
import plus.qdt.modules.system.entity.dos.Setting;
import plus.qdt.modules.system.entity.dto.OssSetting;
import plus.qdt.modules.system.entity.enums.SettingEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 文件服务抽象工厂 直接返回操作类
 *
 * <AUTHOR>
 * @version v1.0
 * 2022-06-06 11:35
 */
@Component
@RequiredArgsConstructor
public class FilePluginFactory {


    private final SettingClient settingClient;


    /**
     * 获取oss client
     *
     * @return
     */
    public FilePlugin filePlugin() {

        OssSetting ossSetting;
        try {
            Setting setting = settingClient.get(SettingEnum.OSS_SETTING.name());

            ossSetting = JSONUtil.toBean(setting.getSettingValue(), OssSetting.class);


            return switch (OssEnum.valueOf(ossSetting.getType())) {
                case MINIO -> new MinioFilePlugin(ossSetting);
                case ALI_OSS -> new AliFilePlugin(ossSetting);
                case HUAWEI_OBS -> new HuaweiFilePlugin(ossSetting);
                case TENCENT_COS -> new TencentFilePlugin(ossSetting);
            };
        } catch (Exception e) {
            throw new ServiceException();
        }
    }


}
