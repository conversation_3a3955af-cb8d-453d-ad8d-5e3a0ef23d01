package plus.qdt.modules.other.mapper;

import plus.qdt.modules.page.entity.dos.PageData;
import plus.qdt.modules.page.entity.vos.PageDataListVO;
import plus.qdt.modules.page.entity.vos.PageDataVO;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 楼层装修设置数据处理层
 *
 * <AUTHOR>
 * @since 2020/12/7 11:26
 */
public interface PageDataMapper extends BaseMapper<PageData> {


    /**
     * 获取页面数量
     *
     * @param queryWrapper 查询条件
     * @return 页面数量
     */
    @Select("SELECT COUNT(id) FROM li_page_data ${ew.customSqlSegment}")
    Integer getPageDataNum(@Param(Constants.WRAPPER) Wrapper<Integer> queryWrapper);

}