package plus.qdt.modules.system.serviceimpl;

import plus.qdt.common.enums.ResultCode;
import plus.qdt.common.exception.ServiceException;
import plus.qdt.common.utils.StringUtils;
import plus.qdt.modules.system.entity.dos.AppVersion;
import plus.qdt.modules.system.mapper.AppVersionMapper;
import plus.qdt.modules.system.service.AppVersionService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;


/**
 * APP版本控制业务层实现
 *
 * <AUTHOR>
 * @since 2020/11/17 8:02 下午
 */
@Service
public class AppVersionServiceImpl extends ServiceImpl<AppVersionMapper, AppVersion> implements AppVersionService {

    @Override
    public AppVersion getAppVersion(String appType) {
        return this.baseMapper.getLatestVersion(appType);
    }

    @Override
    public boolean checkAppVersion(AppVersion appVersion) {
        if (null == appVersion) {
            throw new ServiceException(ResultCode.APP_VERSION_PARAM_ERROR);
        }
        if (StringUtils.isBlank(appVersion.getType())) {
            throw new ServiceException(ResultCode.APP_VERSION_TYPE_ERROR);
        }
        //检测版本是否存在
        if (null != this.getOne(new LambdaQueryWrapper<AppVersion>()
                .eq(AppVersion::getVersion, appVersion.getVersion())
                .eq(AppVersion::getType, appVersion.getType())
                .ne(appVersion.getId() != null, AppVersion::getId, appVersion.getId()))) {
            throw new ServiceException(ResultCode.APP_VERSION_EXIST);
        }
        return true;
    }
}
