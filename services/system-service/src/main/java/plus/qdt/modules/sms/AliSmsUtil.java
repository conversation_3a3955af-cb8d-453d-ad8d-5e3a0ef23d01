package plus.qdt.modules.sms;

import plus.qdt.modules.sms.entity.dos.SmsSign;
import plus.qdt.modules.sms.entity.dos.SmsTemplate;

import java.util.Map;

/**
 * <AUTHOR>
 * @version v4.1
 * @since 2021/2/1 6:05 下午
 */
public interface AliSmsUtil {
    /**
     * 申请短信签名
     *
     * @param smsSign 短信签名
     * @throws Exception 阿里短信签名错误
     */
    void addSmsSign(SmsSign smsSign);


    /**
     * 删除短信签名
     *
     * @param signName 签名名称
     * @throws Exception 阿里短信签名错误
     */
    void deleteSmsSign(String signName);

    /**
     * 查询短信签名申请状态
     *
     * @param signName 签名名称
     * @return 短信签名申请状态
     * @throws Exception 阿里短信签名错误
     */
    Map<String, Object> querySmsSign(String signName);

    /**
     * 修改未审核通过的短信签名，并重新提交审核。
     *
     * @param smsSign 短信签名
     * @throws Exception 阿里短信签名错误
     */
    void modifySmsSign(SmsSign smsSign);

    /**
     * 修改未审核通过的短信模板，并重新提交审核。
     *
     * @param smsTemplate 短信模板
     * @throws Exception 阿里短信签名错误
     */
    void modifySmsTemplate(SmsTemplate smsTemplate);

    /**
     * 查看短信模板
     *
     * @param templateCode 短信模板CODE
     * @return 短信模板
     * @throws Exception 阿里短信签名错误
     */
    Map<String, Object> querySmsTemplate(String templateCode);

    /**
     * 申请短信模板
     *
     * @param smsTemplate 短信模板
     * @return 短信模板
     * @throws Exception 阿里短信签名错误
     */
    String addSmsTemplate(SmsTemplate smsTemplate);

    /**
     * 删除短信模板
     *
     * @param templateCode 短信模板CODE
     * @throws Exception 阿里短信签名错误
     */
    void deleteSmsTemplate(String templateCode);
}
