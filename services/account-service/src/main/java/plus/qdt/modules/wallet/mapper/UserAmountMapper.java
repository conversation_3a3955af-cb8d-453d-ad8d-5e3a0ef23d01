package plus.qdt.modules.wallet.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import plus.qdt.modules.domain.UserAmount;

import java.math.BigDecimal;

/**
 * 账号资产
 *
 * <AUTHOR>
 * @since 2.0
 */
public interface UserAmountMapper extends BaseMapper<UserAmount> {

    /**
     * 获取财通余额
     * @param id 账户id
     * @return {@link BigDecimal}
     * <AUTHOR>
     */
    BigDecimal queryAccount(String id);

    /**
     * 获取财通余额
     * @param id 账户id
     * @return {@link BigDecimal}
     * <AUTHOR>
     */
    BigDecimal queryAccountForLock(String id);

    /**
     * 获取企通余额
     * @param id 账户ID
     * @return {@link BigDecimal}
     * <AUTHOR>
     */
    BigDecimal queryQiAccount(String id);

    /**
     * 获取企通余额
     * @param id 账户ID
     * @return {@link BigDecimal}
     * <AUTHOR>
     */
    BigDecimal queryQiAccountForLock(String id);

    /**
     * 用户财通总额
     * @return {@link BigDecimal}
     * <AUTHOR>
     */
    BigDecimal sumMoney();
}
