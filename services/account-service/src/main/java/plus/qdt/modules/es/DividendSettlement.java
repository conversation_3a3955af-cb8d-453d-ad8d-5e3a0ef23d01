package plus.qdt.modules.es;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * 分红结算实体
 *
 * <AUTHOR>
 * @since 2.0
 */
@Data
@Accessors(chain = true)
public class DividendSettlement {

    @Id
    private String id;

    /**
     * 订单编号
     */
    @Field(type = FieldType.Text)
    private String orderSn;

    /**
     * 子订单单号
     */
    @Field(type = FieldType.Text)
    private String orderItemSn;

    /**
     * 订单销售金额
     */
    @Field(type = FieldType.Double)
    private Double orderMoney;

    /**
     * 类型:
     * 1.自营供应商分红结算
     * 2.自营商城分红
     * 3.非自营商城分红结算
     */
    @Field(type = FieldType.Integer)
    private Integer type;

    /**
     * 商品类型
     * 1.自建商品
     * 2.厂家代理商品
     */
    @Field(type = FieldType.Integer)
    private Integer goodsType;

    /**
     * 代理价
     */
    @Field(type = FieldType.Double)
    private Double proxyMoney;

    /**
     * 成本金额
     */
    @Field(type = FieldType.Double)
    private Double costMoney;

    /**
     * 最终计算金额
     */
    @Field(type = FieldType.Double)
    private Double money;

    /**
     * 分红比例
     */
    @Field(type = FieldType.Float)
    private Double rate;

    /**
     * 平台抽成
     */
    @Field(type = FieldType.Float)
    private Double platformRate;

    /**
     * 创建时间
     */
    @Field(type = FieldType.Date)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime = new Date();

    /**
     * 最终分红金额=(代理价-成本价)*分红比例
     * 最终分红金额=(代理价-成本价)*分红比例*(平台抽成比例/100)
     * @return {@link Double}
     * <AUTHOR>
     */
    public Double getMoney() {
        // 如果抽成不为null
        if (this.platformRate != null) {
            return BigDecimal.valueOf((this.proxyMoney - this.costMoney) * this.rate * (this.platformRate / 100))
                    .setScale(2, RoundingMode.HALF_UP).doubleValue();
        }
        return BigDecimal.valueOf((this.proxyMoney - this.costMoney) * this.rate)
                .setScale(2, RoundingMode.HALF_UP).doubleValue();
    }
}
