package plus.qdt.modules.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import plus.qdt.mybatis.model.BaseStandardEntity;

import java.math.BigDecimal;

/**
 * 金通宝账号;
 *
 * <AUTHOR> yegy
 * @date : 2025-4-28
 */
@Data
@TableName("qdt_jintong_account_log")
@EqualsAndHashCode(callSuper = true)
public class JinTongAccountLog extends BaseStandardEntity {

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;
    /**
     * 金额
     */
    @Schema(description = "金额")
    private BigDecimal money;
    /**
     * 余额
     */
    @Schema(description = "余额")
    private BigDecimal balance;
    /**
     * 业务id，比如是用户操作就用户id
     */
    @Schema(description = "业务id")
    private String businessId;
    /**
     * 流水类型，入账还是出账。0是加，1是减
     */
    @Schema(description = "流水类型")
    private Integer balanceType;
    /**
     * 订单子类型
     */
    @Schema(description = "订单子类型")
    private Integer type;
}
