package plus.qdt.modules.wallet.serviceimpl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import plus.qdt.common.enums.ResultCode;
import plus.qdt.common.exception.ServiceException;
import plus.qdt.common.security.AuthUser;
import plus.qdt.common.security.context.UserContext;
import plus.qdt.common.utils.DateUtil;
import plus.qdt.common.utils.StringUtils;
import plus.qdt.modules.domain.RedPacket;
import plus.qdt.modules.domain.UserAmount;
import plus.qdt.modules.domain.dto.RedPacketUserDto;
import plus.qdt.modules.factory.consume.UserAmountConsume;
import plus.qdt.modules.jinTongAccount.entity.AccountConsume;
import plus.qdt.modules.jinTongAccount.enums.UserAmountLogEnum;
import plus.qdt.modules.member.client.UserClient;
import plus.qdt.modules.member.entity.dos.User;
import plus.qdt.modules.wallet.mapper.RedPacketMapper;
import plus.qdt.modules.wallet.service.RedPacketService;
import plus.qdt.modules.wallet.service.UserAmountService;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 用户红包记录;(qdt_user_red_packet)表服务实现类
 * <AUTHOR>
 * @since 2.0
 */
@Service
public class RedPacketServiceImpl extends ServiceImpl<RedPacketMapper, RedPacket> implements RedPacketService {

    @Resource
    private UserAmountConsume userAmountConsume;
    @Resource
    private UserClient userClient;
    @Resource
    private UserAmountService userAmountService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean sendRedPacket(RedPacket redPacket) {
        AuthUser authUser = UserContext.getCurrentExistUser();
        UserAmount amount = userAmountService.getById(authUser.getId());
        if (redPacket.getTotal().compareTo(amount.getCaiTongCoin()) > 0) {
            throw new ServiceException(ResultCode.AMOUNT_NOT_ENOUGH);
        }
        redPacket.setUserId(authUser.getId());
        if (this.save(redPacket)) {
            // 创建红包用户
            userAmountConsume.consume(new AccountConsume()
                    .setSubType(UserAmountLogEnum.HAND_OUT_RED_PACKET.getSubType())
                    .setId(authUser.getId())
                    .setMoney(redPacket.getTotal())
            );
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BigDecimal receive(RedPacket redPacket) {
        redPacket.setReceiveNum(redPacket.getReceiveNum() + 1);
        AuthUser authUser = UserContext.getCurrentExistUser();
        // 领取金额
        BigDecimal money = redPacket.getTotal().divide(BigDecimal.valueOf(redPacket.getNumber()), 2, RoundingMode.HALF_UP);
        List<RedPacketUserDto> list = new ArrayList<>();
        RedPacketUserDto dto = new RedPacketUserDto()
                .setCreateTime(DateUtil.toString(new Date(), "yyyy年MM月dd号 HH:mm"))
                .setNickname(authUser.getNickName())
                .setAvatar(authUser.getFace())
                .setUserId(authUser.getId());
        list.add(dto);
        if (StringUtils.isNotBlank(redPacket.getRemarks())) {
            List<RedPacketUserDto> res = JSONUtil.parseArray(redPacket.getRemarks()).toList(RedPacketUserDto.class);
            // 判断是否领取过红包
            if (res.stream().anyMatch(item -> item.getUserId().equals(authUser.getId()))) {
                return money;
            }
            list.addAll(res);
        }
        redPacket.setRemarks(JSONUtil.toJsonStr(list));
        this.updateById(redPacket);
        // 发送红包人
        User user = userClient.getById(redPacket.getUserId());

        // 领取用户财通入账
        userAmountConsume.consume(new AccountConsume()
                .setId(authUser.getId())
                .setSubType(UserAmountLogEnum.PUT_RED_PACKET.getSubType())
                .setNames(List.of(user.getNickName()))
                .setMoney(money)
        );
        return money;
    }
}
