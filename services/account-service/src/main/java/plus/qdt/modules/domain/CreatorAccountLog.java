package plus.qdt.modules.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import plus.qdt.mybatis.model.BaseStandardEntity;

import java.math.BigDecimal;

/**
 * 创始人分红池流水表
 *
 * <AUTHOR>
 * @since 2.0
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName(value = "qdt_creator_account_log")
public class CreatorAccountLog extends BaseStandardEntity {

    @Schema(title = "订单类型")
    private Integer type;

    @Schema(title = "描述")
    private String description;

    @Schema(title = "金额")
    private BigDecimal money;

    @Schema(title = "余额")
    private BigDecimal balance;

    @Schema(title = "业务id，比如是用户操作就用户id")
    private String businessId;

    @Schema(title = "流水类型，入账还是出账。0是加，1是减")
    private Integer balanceType;

}
