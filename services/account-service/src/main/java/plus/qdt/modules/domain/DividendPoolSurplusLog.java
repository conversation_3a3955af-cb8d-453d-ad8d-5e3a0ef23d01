package plus.qdt.modules.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import plus.qdt.mybatis.model.BaseStandardEntity;

import java.math.BigDecimal;

/**
 * 分红实体;
 *
 * <AUTHOR> yegy
 * @date : 2025-4-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("qdt_dividend_pool_surplus_log")
public class DividendPoolSurplusLog extends BaseStandardEntity {

    /**
     * 分红前的金额
     */
    private BigDecimal money;
    /**
     * 分红后的金额
     */
    private BigDecimal surplus;
}
