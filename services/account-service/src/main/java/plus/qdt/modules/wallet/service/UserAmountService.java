package plus.qdt.modules.wallet.service;

import com.baomidou.mybatisplus.extension.service.IService;
import plus.qdt.common.security.context.UserContext;
import plus.qdt.modules.domain.UserAmount;
import plus.qdt.modules.domain.params.TransferParam;
import plus.qdt.modules.domain.params.WithdrawCashParam;

import java.math.BigDecimal;
import java.util.List;

/**
 * 账户资产服务类
 *
 * <AUTHOR>
 * @since 2.0
 */
public interface UserAmountService extends IService<UserAmount> {

    /**
     * 获取支付缓存key
     * @return {@link String}
     * <AUTHOR>
     */
    default String getKey() {
        return "account:verify:MEMBER:" + UserContext.getCurrentExistUser().getId();
    }

    /**
     * 企通转赠
     * @param param 转增参数
     * @return {@link Boolean}
     * <AUTHOR>
     */
    boolean transfer(TransferParam param);

    /**
     * 财通提现
     * @param param 提现参数
     * @return {@link Boolean}
     * <AUTHOR>
     */
    boolean withdrawCash(WithdrawCashParam param);

    /**
     * 扫码支付
     * @param param 支付参数
     * @return {@link Boolean}
     * <AUTHOR>
     */
    boolean scanPay(TransferParam param);

    /**
     * 获取所有
     * @return {@link List} {@link UserAmount}
     * <AUTHOR>
     */
    List<UserAmount> listSignUser();

    /**
     * 用户财通总额
     * @return {@link BigDecimal}
     * <AUTHOR>
     */
    BigDecimal sumMoney();
}
