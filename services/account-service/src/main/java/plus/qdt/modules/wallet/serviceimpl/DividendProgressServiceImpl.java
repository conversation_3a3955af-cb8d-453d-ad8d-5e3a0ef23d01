package plus.qdt.modules.wallet.serviceimpl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import plus.qdt.common.security.context.UserContext;
import plus.qdt.common.utils.StringUtils;
import plus.qdt.common.vo.PageVO;
import plus.qdt.modules.domain.DividendProgress;
import plus.qdt.modules.domain.vo.OrderBonusInfoVo;
import plus.qdt.modules.domain.vo.OrderBonusVo;
import plus.qdt.modules.order.order.client.OrderItemClient;
import plus.qdt.modules.order.order.entity.dos.OrderItem;
import plus.qdt.modules.wallet.mapper.DividendProgressMapper;
import plus.qdt.modules.wallet.service.DividendProgressService;
import plus.qdt.mybatis.util.PageUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * 分红进度条;(qdt_dividend_progress)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-6-9
 */
@Service
public class DividendProgressServiceImpl extends ServiceImpl<DividendProgressMapper, DividendProgress> implements DividendProgressService {

    @Resource
    private OrderItemClient orderItemClient;

    @Override
    public OrderBonusInfoVo info() {
        LambdaQueryWrapper<DividendProgress> lqw = Wrappers.<DividendProgress>lambdaQuery()
                .eq(DividendProgress::getUserId, UserContext.getCurrentExistUser().getId())
                .select(DividendProgress::getSignMoney, DividendProgress::getBonusMoney);
        List<DividendProgress> list = this.list(lqw);
        OrderBonusInfoVo vo = new OrderBonusInfoVo();
        vo.setTotal(list.size());
        vo.setBonusMoney(list.stream().map(DividendProgress::getBonusMoney).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setSignMoney(list.stream().map(DividendProgress::getSignMoney).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        vo.setUnBonusMoney(vo.getBonusMoney().subtract(vo.getSignMoney()));
        return vo;
    }

    @Override
    public Page<OrderBonusVo> getPage(PageVO page) {
        Page<OrderBonusVo> pageData = new Page<>(page.getPageNumber(), page.getPageSize());
        Page<DividendProgress> data = this.page(PageUtil.initPage(page), Wrappers.<DividendProgress>lambdaQuery()
                .eq(DividendProgress::getUserId, UserContext.getCurrentExistUser().getId())
                .orderByDesc(DividendProgress::getCreateTime)
                .select(DividendProgress::getOrderSn, DividendProgress::getMoney, DividendProgress::getBonusMoney,
                        DividendProgress::getSplitRatio, DividendProgress::getStatus, DividendProgress::getSignMoney)
        );
        pageData.setTotal(data.getTotal());
        List<OrderBonusVo> list = new ArrayList<>();
        for (DividendProgress record : data.getRecords()) {
            OrderBonusVo vo = new OrderBonusVo();
            vo.setOrderSn(record.getOrderSn());
            vo.setMoney(record.getMoney());
            vo.setBonusMoney(record.getBonusMoney());
            vo.setSplitRatio(record.getSplitRatio());
            if (record.getStatus() == 1) {
                vo.setProgress(1.0);
            } else {
                vo.setProgress(record.getSignMoney().divide(record.getBonusMoney(), 2, RoundingMode.HALF_UP)
                        .doubleValue());
            }
            OrderItem item = orderItemClient.getBySn(record.getOrderSn());
            vo.setName(item.getGoodsName());
            vo.setImage(item.getImage());
            vo.setNum(item.getNum());
            if (StringUtils.isNotBlank(item.getSpecs())) {
                JSONObject specs = JSONUtil.parseObj(item.getSpecs());
                specs.remove("images");
                vo.setSpecs(specs.toString());
            }

            list.add(vo);
        }
        pageData.setRecords(list);
        return pageData;
    }
}
