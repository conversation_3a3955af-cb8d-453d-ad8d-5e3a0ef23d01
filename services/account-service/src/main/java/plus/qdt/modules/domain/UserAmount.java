package plus.qdt.modules.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import plus.qdt.mybatis.model.BaseStandardEntity;

import java.math.BigDecimal;

/**
 * 账户资产
 * <AUTHOR>
 * @since 2.0
 */
@Data
@Accessors(chain = true)
@TableName("qdt_user_amount")
@EqualsAndHashCode(callSuper = true)
public class UserAmount extends BaseStandardEntity {

    @NotNull(message = "财通余额不能为空")
    @Schema(title = "财通余额", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal caiTongCoin;

    @NotNull(message = "企通余额不能为空")
    @Schema(title = "企通余额", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal enterpriseCoin;

    @JsonIgnore
    @Schema(title = "钱包密码")
    private String password;

    @JsonIgnore
    @Schema(title = "密码盐")
    private String slot;

    @TableField(exist = false)
    @Schema(title = "财通今日分红")
    private BigDecimal caiTongIncome;

    @TableField(exist = false)
    @Schema(title = "财通累计分红")
    private BigDecimal caiTongIncomeTotal;

    @TableField(exist = false)
    @Schema(title = "企通今日入账")
    private BigDecimal enterpriseIncome;
}
