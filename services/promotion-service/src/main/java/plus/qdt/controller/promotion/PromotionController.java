package plus.qdt.controller.promotion;

import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.vo.PageVO;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.promotion.entity.dos.PromotionGoods;
import plus.qdt.modules.promotion.entity.dto.search.PromotionGoodsSearchParams;
import plus.qdt.modules.promotion.entity.enums.PromotionsStatusEnum;
import plus.qdt.modules.promotion.service.PromotionGoodsService;
import plus.qdt.modules.promotion.service.PromotionService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 促销接口
 *
 * <AUTHOR>
 * @since 2021/2/2
 **/
@RestController
@Tag(name = "促销接口")
@RequestMapping("/promotion")
@RequiredArgsConstructor
public class PromotionController {


    private final PromotionService promotionService;

    private final PromotionGoodsService promotionGoodsService;

    @GetMapping("/current")
    @Operation(summary = "获取当前进行中的促销活动")
    public ResultMessage<Map<String, List<PromotionGoods>>> getCurrentPromotion() {
        Map<String, List<PromotionGoods>> currentPromotion = promotionService.getCurrentPromotion();
        return ResultUtil.data(currentPromotion);
    }

    @GetMapping("/{promotionId}/goods")
    @Operation(summary = "获取当前进行中的促销活动商品")
    public ResultMessage<Page<PromotionGoods>> getPromotionGoods(@PathVariable String promotionId, String promotionType, PageVO pageVO) {
        PromotionGoodsSearchParams searchParams = new PromotionGoodsSearchParams();
        searchParams.setPromotionId(promotionId);
        searchParams.setPromotionType(promotionType);
        searchParams.setPromotionStatus(PromotionsStatusEnum.START.name());
        Page<PromotionGoods> promotionGoods = promotionGoodsService.pageFindAll(searchParams, pageVO);
        return ResultUtil.data(promotionGoods);
    }


}
