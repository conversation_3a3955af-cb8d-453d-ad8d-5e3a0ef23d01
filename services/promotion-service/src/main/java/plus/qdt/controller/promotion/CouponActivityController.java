package plus.qdt.controller.promotion;

import plus.qdt.common.enums.ResultCode;
import plus.qdt.common.exception.ServiceException;
import plus.qdt.common.security.AuthUser;
import plus.qdt.common.security.context.UserContext;
import plus.qdt.common.security.enums.SceneEnums;
import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.vo.PageVO;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.promotion.entity.dos.CouponActivity;
import plus.qdt.modules.promotion.entity.dos.MemberCoupon;
import plus.qdt.modules.promotion.entity.dto.CouponActivityDTO;
import plus.qdt.modules.promotion.entity.dto.CouponActivityTrigger;
import plus.qdt.modules.promotion.entity.enums.CouponActivityTypeEnum;
import plus.qdt.modules.promotion.entity.vos.CouponActivityVO;
import plus.qdt.modules.promotion.service.CouponActivityService;
import plus.qdt.modules.promotion.tools.PromotionTools;
import plus.qdt.mybatis.util.PageUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * 优惠券活动
 *
 * <AUTHOR>
 * @since 2021/5/21 7:11 下午
 */
@RestController
@Tag(name = "优惠券活动接口")
@RequestMapping("/promotion/coupon-activity")
@RequiredArgsConstructor
public class CouponActivityController {

    private final CouponActivityService couponActivityService;

    @Operation(summary = "获取优惠券活动分页")
    @GetMapping
    public ResultMessage<Page<CouponActivity>> getCouponActivityPage(PageVO page) {
        return ResultUtil.data(couponActivityService.page(PageUtil.initPage(page)));
    }

    @GetMapping("/activity")
    @Operation(summary = "自动领取优惠券")
    public ResultMessage<List<MemberCoupon>> activity() {
        if (UserContext.getCurrentUser() == null) {
            return ResultUtil.success();
        }
        return ResultUtil.data(couponActivityService.trigger(
                CouponActivityTrigger.builder()
                        .couponActivityTypeEnum(CouponActivityTypeEnum.AUTO_COUPON)
                        .nickName(UserContext.getCurrentUser().getNickName())
                        .userId(UserContext.getCurrentUser().getExtendId())
                        .build())
        );
    }

    @Operation(summary = "获取优惠券活动")
    @GetMapping("/{couponActivityId}")
    public ResultMessage<CouponActivityVO> getCouponActivity(@PathVariable String couponActivityId) {
        return ResultUtil.data(couponActivityService.getCouponActivityVO(couponActivityId));
    }

    @Operation(summary = "添加优惠券活动")
    @PostMapping
    @PutMapping(consumes = "application/json", produces = "application/json")
    public ResultMessage<CouponActivity> addCouponActivity(@RequestBody(required = false) CouponActivityDTO couponActivityDTO) {
        couponActivityDTO.validateParams();
        this.setStoreInfo(couponActivityDTO);
        if (couponActivityService.savePromotions(couponActivityDTO)) {
            return ResultUtil.data(couponActivityDTO);
        }
        return ResultUtil.error(ResultCode.COUPON_ACTIVITY_SAVE_ERROR);
    }

    @Operation(summary = "关闭优惠券活动")
    @DeleteMapping("/{id}")
    public ResultMessage<CouponActivity> updateStatus(@PathVariable String id) {
        if (couponActivityService.updateStatus(Collections.singletonList(id), null, null)) {
            return ResultUtil.success();
        }
        throw new ServiceException();
    }

    private void setStoreInfo(CouponActivityDTO couponActivityDTO) {
        AuthUser currentUser = UserContext.getCurrentUser();
        if (currentUser == null) {
            throw new ServiceException(ResultCode.USER_NOT_EXIST);
        }
        if (SceneEnums.MANAGER.equals(currentUser.getScene())) {
            couponActivityDTO.setStoreId(PromotionTools.PLATFORM_ID);
            couponActivityDTO.setStoreName(PromotionTools.PLATFORM_NAME);
        } else {
            couponActivityDTO.setStoreId(currentUser.getExtendId());
            couponActivityDTO.setStoreName(currentUser.getExtendName());
        }
    }
}
