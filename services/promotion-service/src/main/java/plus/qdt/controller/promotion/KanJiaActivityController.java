package plus.qdt.controller.promotion;

import cn.hutool.core.text.CharSequenceUtil;
import plus.qdt.common.security.context.UserContext;
import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.vo.PageVO;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.promotion.entity.dos.KanjiaActivity;
import plus.qdt.modules.promotion.entity.dos.KanjiaActivityLog;
import plus.qdt.modules.promotion.entity.dto.search.KanJiaActivityLogQuery;
import plus.qdt.modules.promotion.entity.dto.search.KanjiaActivityGoodsParams;
import plus.qdt.modules.promotion.entity.dto.search.KanjiaActivityQuery;
import plus.qdt.modules.promotion.entity.dto.search.KanjiaActivitySearchParams;
import plus.qdt.modules.promotion.entity.enums.PromotionsStatusEnum;
import plus.qdt.modules.promotion.entity.vos.kanjia.KanjiaActivityGoodsListVO;
import plus.qdt.modules.promotion.entity.vos.kanjia.KanjiaActivityGoodsVO;
import plus.qdt.modules.promotion.entity.vos.kanjia.KanjiaActivityVO;
import plus.qdt.modules.promotion.service.KanjiaActivityGoodsService;
import plus.qdt.modules.promotion.service.KanjiaActivityLogService;
import plus.qdt.modules.promotion.service.KanjiaActivityService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 买家端,砍价活动商品
 *
 * <AUTHOR>
 * @date 2021/7/12
 **/
@RestController
@Tag(name = "买家端,砍价商品接口")
@RequestMapping("/promotion/haggle/activity")
@RequiredArgsConstructor
public class KanJiaActivityController {

    private final KanjiaActivityGoodsService kanJiaActivityGoodsService;

    private final KanjiaActivityLogService kanJiaActivityLogService;

    private final KanjiaActivityService kanJiaActivityService;

    @GetMapping
    @Operation(summary = "分页获取砍价商品")
    public ResultMessage<Page<KanjiaActivityGoodsListVO>> kanjiaActivityGoodsPage(KanjiaActivityGoodsParams kanjiaActivityGoodsParams, PageVO page) {
        // 会员端查询到的肯定是已经开始的活动商品
        kanjiaActivityGoodsParams.setPromotionStatus(PromotionsStatusEnum.START.name());
        return ResultUtil.data(kanJiaActivityGoodsService.kanjiaGoodsVOPage(kanjiaActivityGoodsParams, page));
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取砍价活动商品")
    public ResultMessage<KanjiaActivityGoodsVO> getKanjiaActivityGoods(@PathVariable String id) {
        return ResultUtil.data(kanJiaActivityGoodsService.getKanJiaGoodsVO(id));
    }

    @GetMapping("/getKanjiaActivity/logs")
    @Operation(summary = "分页获取砍价活动-帮砍记录")
    public ResultMessage<Page<KanjiaActivityLog>> getKanjiaActivityLog(KanJiaActivityLogQuery kanJiaActivityLogQuery, PageVO page) {
        return ResultUtil.data(kanJiaActivityLogService.getForPage(kanJiaActivityLogQuery, page));
    }

    @PostMapping("/getKanjiaActivity")
    @Operation(summary = "获取砍价活动")
    public ResultMessage<KanjiaActivityVO> getKanJiaActivity(KanjiaActivitySearchParams kanjiaActivitySearchParams) {
        //如果是非被邀请关系则填写会员ID
        if (CharSequenceUtil.isEmpty(kanjiaActivitySearchParams.getKanjiaActivityId())) {
            kanjiaActivitySearchParams.setMemberId(UserContext.getCurrentUser().getExtendId());
        }
        return ResultUtil.data(kanJiaActivityService.getKanjiaActivityVO(kanjiaActivitySearchParams));
    }

    @PostMapping
    @Operation(summary = "发起砍价活动")
    public ResultMessage<KanjiaActivityLog> launchKanJiaActivity(String id) {
        KanjiaActivityLog kanjiaActivityLog = kanJiaActivityService.add(id);
        return ResultUtil.data(kanjiaActivityLog);
    }

    @PostMapping("/help/{kanjiaActivityId}")
    @Operation(summary = "帮砍一刀")
    public ResultMessage<KanjiaActivityLog> helpKanJia(@PathVariable String kanjiaActivityId) {
        KanjiaActivityLog kanjiaActivityLog = kanJiaActivityService.helpKanJia(kanjiaActivityId);
        return ResultUtil.data(kanjiaActivityLog);
    }

    @GetMapping("/kanjiaActivity/mine/")
    @Operation(summary = "分页获取已参与的砍价活动")
    public ResultMessage<Page<KanjiaActivity>> getPointsGoodsPage(KanjiaActivityQuery kanjiaActivityQuery, PageVO page) {
        // 会员端查询到的肯定是已经开始的活动商品
        kanjiaActivityQuery.setMemberId(UserContext.getCurrentUser().getExtendId());
        Page<KanjiaActivity> kanjiaActivity = kanJiaActivityService.getForPage(kanjiaActivityQuery, page);
        return ResultUtil.data(kanjiaActivity);
    }

}
