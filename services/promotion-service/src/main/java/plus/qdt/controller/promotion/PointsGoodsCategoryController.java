package plus.qdt.controller.promotion;

import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.vo.PageVO;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.promotion.entity.dos.PointsGoodsCategory;
import plus.qdt.modules.promotion.entity.vos.PointsGoodsCategoryVO;
import plus.qdt.modules.promotion.service.PointsGoodsCategoryService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 积分商品分类接口
 *
 * <AUTHOR>
 * @since 2021/1/14
 **/
@RestController
@Tag(name = "积分商品分类接口")
@RequestMapping("/promotion/points/category")
@RequiredArgsConstructor
public class PointsGoodsCategoryController {

    private final PointsGoodsCategoryService pointsGoodsCategoryService;

    @PostMapping
    @Operation(summary = "添加积分商品分类")
    public ResultMessage<Object> add(@RequestBody PointsGoodsCategoryVO pointsGoodsCategory) {
        pointsGoodsCategoryService.addCategory(pointsGoodsCategory);
        return ResultUtil.success();
    }

    @PutMapping
    @Operation(summary = "修改积分商品分类")
    public ResultMessage<Object> update(@RequestBody PointsGoodsCategoryVO pointsGoodsCategory) {
        pointsGoodsCategoryService.updateCategory(pointsGoodsCategory);
        return ResultUtil.success();
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除积分商品分类")
    public ResultMessage<Object> delete(@PathVariable String id) {
        pointsGoodsCategoryService.deleteCategory(id);
        return ResultUtil.success();
    }

    @GetMapping
    @Operation(summary = "获取积分商品分类分页")
    public ResultMessage<Page<PointsGoodsCategory>> page(String name, PageVO page) {
        return ResultUtil.data(pointsGoodsCategoryService.getCategoryByPage(name, page));
    }

    @GetMapping("/{id}")
    @Operation(summary = "修改积分商品分类")
    public ResultMessage<Object> getById(@PathVariable String id) {
        return ResultUtil.data(pointsGoodsCategoryService.getCategoryDetail(id));
    }

}
