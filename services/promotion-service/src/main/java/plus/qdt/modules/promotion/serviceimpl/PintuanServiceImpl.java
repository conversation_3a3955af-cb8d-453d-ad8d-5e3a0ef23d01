package plus.qdt.modules.promotion.serviceimpl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import plus.qdt.common.enums.PromotionTypeEnum;
import plus.qdt.common.enums.ResultCode;
import plus.qdt.common.exception.ServiceException;
import plus.qdt.common.utils.CurrencyUtil;
import plus.qdt.exchange.AmqpExchangeProperties;
import plus.qdt.modules.goods.client.GoodsClient;
import plus.qdt.modules.goods.entity.dos.GoodsSku;
import plus.qdt.modules.member.client.UserClient;
import plus.qdt.modules.member.entity.dos.User;
import plus.qdt.modules.order.order.client.OrderClient;
import plus.qdt.modules.order.order.entity.dos.Order;
import plus.qdt.modules.order.order.entity.enums.OrderStatusEnum;
import plus.qdt.modules.order.order.entity.enums.PayStatusEnum;
import plus.qdt.modules.promotion.entity.dos.Pintuan;
import plus.qdt.modules.promotion.entity.dos.PromotionGoods;
import plus.qdt.modules.promotion.entity.dto.search.PintuanSearchParams;
import plus.qdt.modules.promotion.entity.dto.search.PromotionGoodsSearchParams;
import plus.qdt.modules.promotion.entity.enums.PromotionsScopeTypeEnum;
import plus.qdt.modules.promotion.entity.enums.PromotionsStatusEnum;
import plus.qdt.modules.promotion.entity.vos.PintuanMemberVO;
import plus.qdt.modules.promotion.entity.vos.PintuanShareVO;
import plus.qdt.modules.promotion.entity.vos.PintuanVO;
import plus.qdt.modules.promotion.mapper.PintuanMapper;
import plus.qdt.modules.promotion.service.PintuanService;
import plus.qdt.modules.promotion.service.PromotionGoodsService;
import plus.qdt.modules.promotion.tools.PromotionTools;
import plus.qdt.routing.PromotionRoutingKey;
import plus.qdt.trigger.enums.DelayTypeEnums;
import plus.qdt.trigger.interfaces.TimeTrigger;
import plus.qdt.trigger.model.TimeExecuteConstant;
import plus.qdt.trigger.model.TimeTriggerMsg;
import plus.qdt.trigger.util.DelayQueueTools;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 拼团业务层实现
 *
 * <AUTHOR>
 * @since 2020/8/21
 */
@Service
@RequiredArgsConstructor
public class PintuanServiceImpl extends AbstractPromotionsServiceImpl<PintuanMapper, Pintuan> implements PintuanService {

    /**
     * 促销商品
     */
    private final PromotionGoodsService promotionGoodsService;
    /**
     * 规格商品
     */
    private final GoodsClient goodsClient;
    /**
     * 会员
     */
    private final UserClient userClient;
    /**
     * 订单
     */
    private final OrderClient orderClient;


    private final AmqpExchangeProperties amqpExchangeProperties;

    /**
     * 延时任务
     */
    private final TimeTrigger timeTrigger;


    /**
     * 获取当前拼团的会员
     *
     * @param pintuanId 拼图id
     * @return 当前拼团的会员列表
     */
    @Override
    public List<PintuanMemberVO> getPintuanMember(String pintuanId, String skuId) {
        List<PintuanMemberVO> members = new ArrayList<>();
        Pintuan pintuan = this.getById(pintuanId);
        if (pintuan == null) {
            log.error("拼团活动为" + pintuanId + "的拼团活动不存在！");
            return new ArrayList<>();
        }
        List<Order> orders = orderClient.queryListByPromotionId(PromotionTypeEnum.PINTUAN.name(), OrderStatusEnum.PAID.name(), "", pintuanId, skuId);
        //遍历订单状态为已支付，为团长的拼团订单
        for (Order order : orders) {
            User member = userClient.getById(order.getBuyerId());
            PintuanMemberVO memberVO = new PintuanMemberVO(member);
            //获取已参团人数
            this.setMemberVONum(memberVO, pintuan.getRequiredNum(), order);
            memberVO.setOrderSn(order.getSn());
            if (memberVO.getToBeGroupedNum() > 0) {
                members.add(memberVO);
            }
        }
        return members;
    }

    /**
     * 查询拼团活动详情
     *
     * @param id 拼团ID
     * @return 拼团活动详情
     */
    @Override
    public PintuanVO getPintuanVO(String id) {
        Pintuan pintuan = this.getById(id);
        if (pintuan == null) {
            log.error("拼团活动id[" + id + "]的拼团活动不存在！");
            throw new ServiceException(ResultCode.PINTUAN_NOT_EXIST_ERROR);
        }
        PintuanVO pintuanVO = new PintuanVO(pintuan);
        PromotionGoodsSearchParams searchParams = new PromotionGoodsSearchParams();
        searchParams.setPromotionId(pintuan.getId());
        pintuanVO.setPromotionGoodsList(promotionGoodsService.listFindAll(searchParams));
        return pintuanVO;
    }

    /**
     * 查询拼团活动详情
     *
     * @param searchParams 拼团查询参数
     * @return 拼团活动详情
     */
    @Override
    public PintuanVO getPintuanVO(PintuanSearchParams searchParams) {
        Pintuan pintuan = this.getOne(searchParams.queryWrapper(), false);
        if (pintuan == null) {
            log.error("拼团活动不存在！{" + searchParams + "}");
            throw new ServiceException(ResultCode.PINTUAN_NOT_EXIST_ERROR);
        }
        PintuanVO pintuanVO = new PintuanVO(pintuan);
        PromotionGoodsSearchParams promotionGoodsSearchParams = new PromotionGoodsSearchParams();
        promotionGoodsSearchParams.setPromotionId(pintuan.getId());
        pintuanVO.setPromotionGoodsList(promotionGoodsService.listFindAll(promotionGoodsSearchParams));
        return pintuanVO;
    }

    /**
     * 获取拼团分享信息
     *
     * @param parentOrderSn 拼团团长订单sn
     * @param skuId         商品skuId
     * @return 拼团分享信息
     */
    @Override
    public PintuanShareVO getPintuanShareInfo(String parentOrderSn, String skuId) {
        PintuanShareVO pintuanShareVO = new PintuanShareVO();
        pintuanShareVO.setPintuanMemberVOS(new ArrayList<>());
        //查找团长订单和已和当前拼团订单拼团的订单
        List<Order> orders = orderClient.queryListByPromotion(PromotionTypeEnum.PINTUAN.name(), PayStatusEnum.PAID.name(), parentOrderSn, parentOrderSn);
        this.setPintuanOrderInfo(orders, pintuanShareVO, skuId);
        //如果为根据团员订单sn查询拼团订单信息时，找到团长订单sn，然后找到所有参与到同一拼团的订单信息
        if (!orders.isEmpty() && pintuanShareVO.getPromotionGoods() == null) {
            List<Order> parentOrders = orderClient.queryListByPromotion(PromotionTypeEnum.PINTUAN.name(), PayStatusEnum.PAID.name(), orders.getFirst().getParentOrderSn(), orders.getFirst().getParentOrderSn());
            this.setPintuanOrderInfo(parentOrders, pintuanShareVO, skuId);
        }
        return pintuanShareVO;
    }

    /**
     * 更新促销商品信息
     *
     * @param promotions 促销实体
     * @return 是否更新成功
     */
    @Override
    public boolean updatePromotionsGoods(Pintuan promotions) {
        boolean result = super.updatePromotionsGoods(promotions);
        if (!PromotionsStatusEnum.CLOSE.name().equals(promotions.getPromotionStatus())
                && PromotionsScopeTypeEnum.PORTION_GOODS.name().equals(promotions.getScopeType())
                && promotions instanceof PintuanVO pintuanVO) {
            this.updatePintuanPromotionGoods(pintuanVO);

            //发送促销活动开始的延时任务
            this.timeTrigger.addDelay(TimeTriggerMsg.builder()
                    .triggerExecutor(TimeExecuteConstant.PROMOTION_EXECUTOR)
                    .exchange(amqpExchangeProperties.getPromotion())
                    .triggerTime(promotions.getOriginEndTime().getTime())
                    .param(promotions)
                    .uniqueKey(DelayQueueTools.wrapperUniqueKey(DelayTypeEnums.PINTUAN_ORDER, (promotions.getId())))
                    .routingKey(PromotionRoutingKey.PINTUAN).build());
        }
        if (promotions.getOriginEndTime() == null && promotions.getOriginStartTime() == null) {
            //过滤父级拼团订单，根据父级拼团订单分组
            this.orderClient.checkFictitiousOrder(promotions.getId(), promotions.getRequiredNum(), promotions.getFictitious());
        }
        return result;
    }

    /**
     * 更新促销信息到商品索引
     *
     * @param promotions 促销实体
     */
    @Override
    public void updateEsGoodsIndex(Pintuan promotions) {
        Pintuan pintuan = JSONUtil.parse(promotions).toBean(Pintuan.class);
        super.updateEsGoodsIndex(pintuan);
    }

    @Override
    public void checkPromotions(Pintuan promotions) {
        super.checkPromotions(promotions);
        if (promotions instanceof PintuanVO pintuanVO && pintuanVO.getPromotionGoodsList() != null && !pintuanVO.getPromotionGoodsList().isEmpty()) {
            pintuanVO.setScopeId(pintuanVO.getPromotionGoodsList().stream().map(PromotionGoods::getSkuId).reduce((i, j) -> i + "," + j).orElse(""));
        }
    }

    /**
     * 当前促销类型
     *
     * @return 当前促销类型
     */
    @Override
    public PromotionTypeEnum getPromotionType() {
        return PromotionTypeEnum.PINTUAN;
    }

    /**
     * 根据订单信息，从中提取出拼团信息，设置拼团信息
     *
     * @param orders         订单列表
     * @param pintuanShareVO 拼团信息
     * @param skuId          商品skuId（用于获取拼团商品信息）
     */
    private void setPintuanOrderInfo(List<Order> orders, PintuanShareVO pintuanShareVO, String skuId) {
        for (Order order : orders) {
            if (pintuanShareVO.getPintuanMemberVOS().stream().anyMatch(i -> i.getMemberId().equals(order.getBuyerId()))) {
                continue;
            }
            User member = userClient.getById(order.getBuyerId());
            PintuanMemberVO memberVO = new PintuanMemberVO(member);
            if (CharSequenceUtil.isEmpty(order.getParentOrderSn())) {
                memberVO.setOrderSn("");
                PromotionGoodsSearchParams searchParams = new PromotionGoodsSearchParams();
                searchParams.setPromotionId(order.getPromotionId());
                searchParams.setSkuId(skuId);
                PromotionGoods promotionGoods = promotionGoodsService.getPromotionsGoods(searchParams);
                if (promotionGoods == null) {
                    GoodsSku goodsSku = goodsClient.getGoodsSkuByIdFromCache(skuId);
                    promotionGoods = new PromotionGoods(goodsSku);
                    promotionGoods.setPrice(CurrencyUtil.div(order.getGoodsPrice(), order.getGoodsNum()));
                }
                pintuanShareVO.setPromotionGoods(promotionGoods);
                Pintuan pintuanById = this.getById(order.getPromotionId());
                //获取已参团人数
                this.setMemberVONum(memberVO, pintuanById.getRequiredNum(), order);
                if (Boolean.TRUE.equals(pintuanById.getFictitious())) {
                    if (OrderStatusEnum.COMPLETED.name().equals(order.getOrderStatus())) {
                        memberVO.setGroupedNum(pintuanById.getRequiredNum());
                        memberVO.setToBeGroupedNum(0L);
                    } else if (!OrderStatusEnum.PAID.name().equals(order.getOrderStatus()) ) {
                        memberVO.setGroupNum(0);
                        memberVO.setGroupedNum(0);
                        memberVO.setToBeGroupedNum(0);
                    }
                }
            }
            pintuanShareVO.getPintuanMemberVOS().add(memberVO);
        }
    }

    private void setMemberVONum(PintuanMemberVO memberVO, Integer requiredNum, Order order) {
        if (!CharSequenceUtil.equalsAny(order.getOrderStatus(), OrderStatusEnum.PAID.name())) {
            memberVO.setGroupNum(0);
            memberVO.setGroupedNum(0);
            memberVO.setToBeGroupedNum(0);
            return;
        }
        long count = this.orderClient.queryCountByPromotion(PromotionTypeEnum.PINTUAN.name(), PayStatusEnum.PAID.name(), order.getSn(), order.getSn());
        //获取待参团人数
        long toBoGrouped = requiredNum - count;
        memberVO.setGroupNum(requiredNum);
        memberVO.setGroupedNum(count);
        memberVO.setToBeGroupedNum(toBoGrouped);
    }

    /**
     * 更新记录的促销商品信息
     *
     * @param pintuan 拼团信息
     */
    private void updatePintuanPromotionGoods(PintuanVO pintuan) {

        if (pintuan.getPromotionGoodsList() != null && !pintuan.getPromotionGoodsList().isEmpty()) {
            List<PromotionGoods> promotionGoods = PromotionTools.promotionGoodsInit(pintuan.getPromotionGoodsList(), pintuan, PromotionTypeEnum.PINTUAN);
            for (PromotionGoods promotionGood : promotionGoods) {
                promotionGood.setId(null);
                if (goodsClient.getCanPromotionGoodsSkuByIdFromCache(promotionGood.getSkuId()) == null) {
                    log.error("商品[" + promotionGood.getGoodsName() + "]不存在或处于不可售卖状态！");
                    throw new ServiceException("商品[" + promotionGood.getGoodsName() + "]不存在或处于不可售卖状态！");
                }
                //查询是否在同一时间段参与了拼团活动
                Integer count = promotionGoodsService.findInnerOverlapPromotionGoods(PromotionTypeEnum.SECKILL.name(), promotionGood.getSkuId(), pintuan.getOriginStartTime(), pintuan.getOriginEndTime(), pintuan.getId());
                //查询是否在同一时间段参与了限时抢购活动
                count += promotionGoodsService.findInnerOverlapPromotionGoods(PromotionTypeEnum.PINTUAN.name(), promotionGood.getSkuId(), pintuan.getOriginStartTime(), pintuan.getOriginEndTime(), pintuan.getId());
                if (count > 0) {
                    log.error("商品[" + promotionGood.getGoodsName() + "]已经在重叠的时间段参加了秒杀活动或拼团活动，不能参加拼团活动");
                    throw new ServiceException("商品[" + promotionGood.getGoodsName() + "]已经在重叠的时间段参加了秒杀活动或拼团活动，不能参加拼团活动");
                }
            }
            PromotionGoodsSearchParams searchParams = new PromotionGoodsSearchParams();
            searchParams.setPromotionId(pintuan.getId());
            searchParams.setPromotionType(PromotionTypeEnum.PINTUAN.name());
            promotionGoodsService.deletePromotionGoods(searchParams);
            promotionGoodsService.saveBatch(promotionGoods);
        }
    }

}