package plus.qdt.modules.promotion.serviceimpl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ArrayUtil;
import plus.qdt.common.enums.PromotionTypeEnum;
import plus.qdt.common.utils.GsonUtils;
import plus.qdt.exchange.AmqpExchangeProperties;
import plus.qdt.modules.promotion.entity.dos.Seckill;
import plus.qdt.modules.promotion.entity.dos.SeckillApply;
import plus.qdt.modules.promotion.entity.dto.search.SeckillSearchParams;
import plus.qdt.modules.promotion.entity.enums.PromotionsApplyStatusEnum;
import plus.qdt.modules.promotion.mapper.SeckillMapper;
import plus.qdt.modules.promotion.service.SeckillService;
import plus.qdt.modules.promotion.tools.PromotionTools;
import plus.qdt.modules.system.client.SettingClient;
import plus.qdt.modules.system.entity.dos.Setting;
import plus.qdt.modules.system.entity.dto.SeckillSetting;
import plus.qdt.modules.system.entity.enums.SettingEnum;
import plus.qdt.routing.GoodsRoutingKey;
import plus.qdt.util.AmqpMessage;
import plus.qdt.util.AmqpSender;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 秒杀活动业务层实现
 *
 * <AUTHOR>
 * @since 2020/8/21
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SeckillServiceImpl extends AbstractPromotionsServiceImpl<SeckillMapper, Seckill> implements SeckillService {


    /**
     * 设置
     */
    private final SettingClient settingClient;


    private final AmqpSender amqpSender;

    private final AmqpExchangeProperties amqpExchangeProperties;


    /**
     * 生成秒杀活动
     *
     * @return 是否成功生成秒杀活动
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean generatorSeckill() {
        Setting setting = settingClient.get(SettingEnum.SECKILL_SETTING.name());
        SeckillSetting seckillSetting = GsonUtils.fromJson(setting.getSettingValue(), SeckillSetting.class);
        log.info("生成秒杀活动设置：{}", seckillSetting);
        for (int i = 1; i <= seckillSetting.getCreation(); i++) {
            Seckill seckill = new Seckill(i, seckillSetting.getHours(), seckillSetting.getSeckillRule());
            seckill.setApplyEndTime(null);
            //如果已经存在促销，则不再次保存
            if (this.list(PromotionTools.checkActiveTime(DateUtil.beginOfDay(seckill.getOriginStartTime()), DateUtil.endOfDay(seckill.getOriginEndTime()), PromotionTypeEnum.SECKILL, null, seckill.getId())).isEmpty()) {
                boolean result = super.savePromotions(seckill);
                log.info("生成秒杀活动参数：{},结果：{}", seckill, result);
            }
        }
        return true;
    }

    @Override
    public long getApplyNum() {
        DateTime now = DateUtil.date();
        LambdaQueryWrapper<Seckill> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(Seckill::getApplyEndTime, now);
        queryWrapper.le(Seckill::getStartTime, now);
        queryWrapper.ge(Seckill::getEndTime, now);
        return this.count(queryWrapper);
    }

    @Override
    @Transactional
    public void updateSeckillGoodsNum(String seckillId, Long num) {
        Seckill seckill = this.getById(seckillId);
        if (seckill != null) {
            SeckillSearchParams searchParams = new SeckillSearchParams();
            searchParams.setSeckillId(seckillId);
            LambdaUpdateWrapper<Seckill> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(Seckill::getId, seckillId);
            updateWrapper.set(Seckill::getGoodsNum, num);
            this.update(updateWrapper);

        }
    }

    /**
     * 通用促销更新
     *
     * @param promotions 促销信息
     * @return 是否更新成功
     */
    @Override
    @Transactional
    public boolean updatePromotions(Seckill promotions) {
        this.initPromotion(promotions);
        this.checkStatus(promotions);
        this.checkPromotions(promotions);
        return this.updateById(promotions);
    }

    /**
     * 更新商品索引限时抢购信息
     *
     * @param seckill 限时抢购信息
     */
    @Override
    @Transactional
    public void updateEsGoodsSeckill(Seckill seckill, List<SeckillApply> seckillApplies) {
        if (seckillApplies != null && !seckillApplies.isEmpty()) {
            // 更新促销范围
            seckill.setScopeId(ArrayUtil.join(seckillApplies.stream().map(SeckillApply::getSkuId).toArray(), ","));
            UpdateWrapper<Seckill> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", seckill.getId());
            updateWrapper.set("scope_id", seckill.getScopeId());
            this.update(updateWrapper);
            //循环秒杀商品数据，将数据按照时间段进行存储
            for (SeckillApply seckillApply : seckillApplies) {
                if (seckillApply.getPromotionApplyStatus().equals(PromotionsApplyStatusEnum.PASS.name())) {
                    this.setSeckillApplyTime(seckill, seckillApply);
                }
            }
            if (!seckillApplies.isEmpty()) {
                this.updateEsGoodsIndex(seckill);
            }
        }
    }

    /**
     * 删除商品索引限时抢购信息
     *
     * @param seckill 限时抢购信息
     * @param skuIds  商品skuId列表
     */
    @Override
    public void deleteEsGoodsSeckill(Seckill seckill, List<String> skuIds) {
        Map<Object, Object> params = MapBuilder.create()
                .put("promotionKey", this.getPromotionType() + "-" + seckill.getId())
                .put("scopeId", ArrayUtil.join(skuIds.toArray(), ","))
                .build();

        amqpSender.send(
                AmqpMessage.builder()
                        .exchange(amqpExchangeProperties.getGoods())
                        .routingKey(GoodsRoutingKey.DELETE_GOODS_INDEX_PROMOTIONS)
                        .message(params)
                        .build()
        );

    }

    @Override
    public void setSeckillApplyTime(Seckill seckill, SeckillApply seckillApply) {
        //下一个时间，默认为当天结束时间
        int nextHour = PromotionTools.nextHour(seckill.getHours().split(","), seckillApply.getTimeLine());

        String format = DateUtil.format(seckill.getOriginStartTime(), DatePattern.NORM_DATE_PATTERN);
        DateTime parseStartTime = DateUtil.parse((format + " " + seckillApply.getTimeLine()), "yyyy-MM-dd HH");
        DateTime parseEndTime = DateUtil.parse((format + " " + nextHour), "yyyy-MM-dd HH");
        //如果是当天最后的时间段则设置到当天结束时间的59分59秒
        if (nextHour == seckillApply.getTimeLine()) {
            parseEndTime = DateUtil.parse((format + " " + nextHour + ":59:59"), DatePattern.NORM_DATETIME_PATTERN);
        }
        seckill.setStartTime(parseStartTime);
        //当时商品的秒杀活动活动结束时间为下个时间段的开始
        seckill.setEndTime(parseEndTime);
    }

    /**
     * 初始化促销字段
     *
     * @param promotions 促销实体
     */
    @Override
    public void initPromotion(Seckill promotions) {
        super.initPromotion(promotions);
        if (promotions.getOriginStartTime() != null && promotions.getOriginEndTime() == null) {
            promotions.setEndTime(DateUtil.endOfDay(promotions.getOriginStartTime()));
        }
        if (promotions.getOriginStartTime() != null && CharSequenceUtil.isNotEmpty(promotions.getHours())) {
            Integer[] split = Convert.toIntArray(promotions.getHours().split(","));
            Arrays.sort(split);
            String startTimeStr = DateUtil.format(promotions.getOriginStartTime(), DatePattern.NORM_DATE_PATTERN) + " " + split[0] + ":00";
            promotions.setStartTime(DateUtil.parse(startTimeStr, DatePattern.NORM_DATETIME_MINUTE_PATTERN));
            promotions.setEndTime(DateUtil.endOfDay(promotions.getOriginStartTime()));
        }
        if (promotions.getApplyEndTime() == null && promotions.getOriginStartTime() != null) {
            promotions.setApplyEndTime(promotions.getOriginStartTime());
        }
    }

    /**
     * 当前促销类型
     *
     * @return 当前促销类型
     */
    @Override
    public PromotionTypeEnum getPromotionType() {
        return PromotionTypeEnum.SECKILL;
    }
}