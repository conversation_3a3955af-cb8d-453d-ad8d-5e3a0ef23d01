package plus.qdt.controller.order;

import plus.qdt.common.aop.annotation.PreventDuplicateSubmissions;
import plus.qdt.common.security.AuthUser;
import plus.qdt.common.security.context.UserContext;
import plus.qdt.common.security.enums.SceneEnums;
import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.utils.StringUtils;
import plus.qdt.common.vo.PageVO;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.order.order.entity.dos.OrderFlow;
import plus.qdt.modules.order.order.entity.dto.OrderFlowQueryDTO;
import plus.qdt.modules.order.order.entity.dto.OrderItemFlowSearchParams;
import plus.qdt.modules.order.order.entity.enums.OrderFlowStatusEnum;
import plus.qdt.modules.order.order.entity.enums.OrderItemAfterSaleStatusEnum;
import plus.qdt.modules.order.order.entity.vo.OrderFlowStatisticsVO;
import plus.qdt.modules.order.order.entity.vo.OrderItemFlowUserVO;
import plus.qdt.modules.order.order.service.OrderFlowService;
import plus.qdt.modules.order.order.service.OrderFlowStatisticsService;
import plus.qdt.modules.order.order.service.OrderIntegrationService;
import plus.qdt.modules.order.order.service.OrderItemFlowService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 订单流水控制器
 *
 * <AUTHOR> @ gmail.com)
 * @version v4.0
 * @Description:
 * @since 2023/4/14 16:39
 */
@RestController
@RequestMapping("/order/flow")
@Tag(name = "订单流水API")
@RequiredArgsConstructor
public class OrderFlowController {

    private final OrderFlowService orderFlowService;

    private final OrderItemFlowService orderItemFlowService;

    private final OrderFlowStatisticsService orderFlowStatisticsService;

    private final OrderIntegrationService orderIntegrationService;

    @Operation(summary = "分页获取订单流水")
    @GetMapping
    public ResultMessage<Page<OrderFlow>> page(OrderFlowQueryDTO queryDTO) {
        AuthUser currentUser = Objects.requireNonNull(UserContext.getCurrentUser());
        if (SceneEnums.STORE.equals(currentUser.getScene())) {
            queryDTO.setStoreId(currentUser.getExtendId());
        } else if (SceneEnums.SUPPLIER.equals(currentUser.getScene())) {
            queryDTO.setSupplierId(currentUser.getExtendId());
        }
        return ResultUtil.data(orderFlowService.orderFlowPage(queryDTO));
    }

    @Operation(summary = "分页获取订单流水")
    @GetMapping("/statistics")
    public ResultMessage<OrderFlowStatisticsVO> statistics(OrderFlowQueryDTO queryDTO) {
        return ResultUtil.data(orderFlowStatisticsService.staticsByQueryParams(queryDTO));
    }

    @Operation(summary = "分页获取分销订单流水")
    @GetMapping("/distribution/mine")
    public ResultMessage<Page<OrderItemFlowUserVO>> mineItemFlow(OrderItemFlowSearchParams searchParams) {
        AuthUser currentUser = Objects.requireNonNull(UserContext.getCurrentUser());
        searchParams.setDistributionId(currentUser.getExtendId());
        searchParams.setAfterSaleStatus(OrderItemAfterSaleStatusEnum.EXPIRED.name());
        return ResultUtil.data(orderItemFlowService.getOrderFlowUserVOByCondition(searchParams));
    }

    @PreventDuplicateSubmissions
    @Operation(summary = "待审核流水")
    @GetMapping(value = "/waitVerify")
    public ResultMessage<Page<OrderFlow>> waitVerify(PageVO pageVO, String orderSn, String orderFlowStatus) {

        if (StringUtils.isEmpty(orderFlowStatus)) {
            orderFlowStatus = OrderFlowStatusEnum.AUDIT.name();
        }

        return ResultUtil.data(orderFlowService.waitVerify(pageVO, orderSn, orderFlowStatus));
    }

    @PreventDuplicateSubmissions
    @Operation(summary = "相关流水查询：根据某一订单sn查询所有流水信息")
    @GetMapping(value = "/other")
    public ResultMessage<List<OrderFlow>> listByOrderSn(String orderSn) {
        return ResultUtil.data(orderFlowService.listByOrderSn(orderSn));
    }

    @PreventDuplicateSubmissions
    @Operation(summary = "管理员强制所有端审核通过，让订单继续执行")
    @PostMapping(value = "/force")
    public ResultMessage<Object> force(String orderSn) {
        orderIntegrationService.force(orderSn);
        return ResultUtil.success();
    }

    @Operation(summary = "获取订单流水详情")
    @GetMapping("/{id}")
    public ResultMessage<OrderFlow> detail(@PathVariable String id) {
        OrderFlow orderFlow = orderFlowService.orderFlowDetail(id);
        return ResultUtil.data(orderFlow);
    }

    @PreventDuplicateSubmissions
    @Operation(summary = "审核通过")
    @PostMapping(value = "/verify/{id}")
    public ResultMessage<Object> verify(@NotBlank(message = "订单编号不能为空") @PathVariable String id) {
        orderIntegrationService.pass(id);
        return ResultUtil.success();
    }

    @PreventDuplicateSubmissions
    @Operation(summary = "审核拒绝")
    @PostMapping(value = "/refuse/{id}")
    public ResultMessage<Object> refuse(@PathVariable String id) {
        orderIntegrationService.refuse(id);
        return ResultUtil.success();
    }

}
