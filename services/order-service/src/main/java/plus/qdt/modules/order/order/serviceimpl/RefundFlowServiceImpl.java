package plus.qdt.modules.order.order.serviceimpl;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import plus.qdt.modules.jinTongAccount.client.AccountConsumeClient;
import plus.qdt.modules.jinTongAccount.entity.vo.HostingAccountVo;
import plus.qdt.modules.order.aftersale.entity.dos.AfterSale;
import plus.qdt.modules.order.order.entity.dos.*;
import plus.qdt.modules.order.order.entity.dto.OrderFlowQueryDTO;
import plus.qdt.modules.order.order.entity.enums.PayStatusEnum;
import plus.qdt.modules.order.order.mapper.RefundFlowMapper;
import plus.qdt.modules.order.order.mapper.TradeMapper;
import plus.qdt.modules.order.order.service.OrderFlowService;
import plus.qdt.modules.order.order.service.RefundFlowService;
import plus.qdt.modules.payment.client.PaymentClient;
import plus.qdt.modules.payment.entity.dos.RefundLog;
import plus.qdt.modules.payment.entity.dto.RefundParam;
import plus.qdt.modules.payment.entity.enums.PaymentMethodEnum;
import plus.qdt.modules.store.entity.vos.StoreFlowRefundDownloadVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 售后流水业务层实现
 *
 * <AUTHOR>
 * @since 2020/11/17 7:38 下午
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RefundFlowServiceImpl extends ServiceImpl<RefundFlowMapper, RefundFlow> implements RefundFlowService {


    private final PaymentClient paymentClient;
    private final TradeMapper tradeMapper;
    private final OrderFlowService orderFlowService;
    private final AccountConsumeClient accountConsumeClient;


    @Override
    public List<StoreFlowRefundDownloadVO> getStoreFlowRefundDownloadVO(OrderFlowQueryDTO orderFlowQueryDTO) {
        return baseMapper.getStoreFlowRefundDownloadVO(generatorQueryWrapper(orderFlowQueryDTO));
    }

    @Override
    @Transactional
    public void generatorRefundFlow(AfterSale afterSale, OrderItem orderItem, Order order) {
        // 获取托管信息
        HostingAccountVo hostingAccount = accountConsumeClient.getPayMethod(order.getTradeSn());
        Double refundMoney = this.handlerQdtPay(order);
        if (refundMoney == null) {
            return;
        }
        RefundFlow refundFlow = new RefundFlow(afterSale, orderItem, order);

        RefundLog refundLog = paymentClient.refund(RefundParam.builder()
                .sn(refundFlow.getAfterSaleNo())
                .refundReason(refundFlow.getRefundReason())
                .orderSn(refundFlow.getOrderSn())
                .price(refundMoney)
                .paymentMethod(order.getPaymentMethod())
                .payPrice(refundMoney)
                .outTradeNo(order.getOutTradeNo())
                .transactionId(order.getTransactionId())
                .hostingAccount(hostingAccount)
                .userId(order.getBuyerId())
                .build());

        refundFlow.setRefunded(refundLog.getIsRefund());

        refundFlow.setErrorMessage(refundLog.getErrorMessage());

        refundFlow.setOutRefundNo(refundLog.getOutRefundNo());
        refundFlow.setRefundTransactionId(refundLog.getRefundTransactionId());

        orderFlowService.refundOrder(refundFlow, afterSale);

        this.save(refundFlow);
    }

    /**
     * 处理财通支付并返回现金退款金额
     * @param order 订单信息
     * @return {@link Double}
     * <AUTHOR>
     */
    private Double handlerQdtPay(Order order) {
        // 获取交易订单
        Trade trade = tradeMapper.getBySn(order.getTradeSn());
        // 如果是财通支付则不走以下现金退款逻辑
        if (PaymentMethodEnum.COIN.name().equals(trade.getPaymentMethod())) {
            // 财通退款
            accountConsumeClient.refundMoney(order.getSn(), order.getCancelReason(), trade.getPaymentMethod(), false);
            return null;
        }
        // 获取退款现金，如果是现金则是全部金额，如果是组合支付，则需要扣除完财通后再扣除现金
        BigDecimal refundPrice = accountConsumeClient.refundMoney(order.getSn(), order.getCancelReason(), trade.getPaymentMethod(), trade.isCombination());
        if (refundPrice.compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }
        Double refundMoney;
        if (trade.isCombination()) {
            refundMoney = trade.getFlowPrice() - trade.getCaiTongCoin();
        } else {
            refundMoney = order.getFlowPrice();
        }
        return refundMoney;
    }


    /**
     * 生成店铺退款流水
     *
     * @param order 订单信息
     */
    @Override
    @GlobalTransactional
    public void generatorRefundFlow(Order order) {
        // 判断订单是否是付款
        if (!PayStatusEnum.PAID.name().equals((order.getPayStatus())) || order.getFlowPrice() <= 0) {
            return;
        }
        // 获取托管信息
        HostingAccountVo hostingAccount = accountConsumeClient.getPayMethod(order.getTradeSn());
        Double refundMoney = this.handlerQdtPay(order);
        if (refundMoney == null) {
            return;
        }
        // 如果组合支付中返回了现金金额,则进行退款
        RefundFlow refundFlow = new RefundFlow(order);

        RefundLog refundLog = paymentClient.refund(RefundParam.builder()
                .sn(refundFlow.getAfterSaleNo())
                .refundReason(refundFlow.getRefundReason())
                .orderSn(refundFlow.getOrderSn())
                .tradeSn(order.getTradeSn())
                .price(refundMoney)
                .paymentMethod(order.getPaymentMethod())
                .payPrice(refundMoney)
                .outTradeNo(order.getOutTradeNo())
                .transactionId(order.getTransactionId())
                .userId(order.getBuyerId())
                .hostingAccount(hostingAccount)
                .build());

        refundFlow.setRefunded(refundLog.getIsRefund());

        refundFlow.setErrorMessage(refundLog.getErrorMessage());

        refundFlow.setOutRefundNo(refundLog.getOutRefundNo());
        refundFlow.setRefundTransactionId(refundLog.getRefundTransactionId());
        orderFlowService.refundOrder(refundFlow);

        this.save(refundFlow);
    }


    /**
     * 生成查询wrapper
     *
     * @param orderFlowQueryDTO 搜索参数
     * @return 查询wrapper
     */
    private LambdaQueryWrapper<OrderFlow> generatorQueryWrapper(OrderFlowQueryDTO orderFlowQueryDTO) {


        LambdaQueryWrapper<OrderFlow> lambdaQueryWrapper = Wrappers.lambdaQuery();
        //分销订单过滤是否判定
        lambdaQueryWrapper.isNotNull(orderFlowQueryDTO.getJustDistribution() != null && orderFlowQueryDTO.getJustDistribution(),
                OrderFlow::getDistributionSettlementPrice);

        //售后编号判定
        lambdaQueryWrapper.eq(CharSequenceUtil.isNotEmpty(orderFlowQueryDTO.getOrderSn()),
                OrderFlow::getOrderSn, orderFlowQueryDTO.getOrderSn());

        return lambdaQueryWrapper;
    }

}