package plus.qdt.modules.order.order.service;

import plus.qdt.common.vo.PageVO;
import plus.qdt.modules.distribution.entity.dos.DistributionOrder;
import plus.qdt.modules.distribution.entity.dto.DistributionDTO;
import plus.qdt.modules.distribution.entity.vos.DistributionOrderSearchParams;
import plus.qdt.modules.order.aftersale.entity.dos.AfterSale;
import plus.qdt.modules.order.order.entity.dos.Order;
import plus.qdt.modules.order.order.entity.dos.OrderFlow;
import plus.qdt.modules.order.order.entity.dos.OrderItem;
import plus.qdt.modules.order.order.entity.dos.RefundFlow;
import plus.qdt.modules.order.order.entity.dto.OrderFlowQueryDTO;
import plus.qdt.modules.store.entity.vos.StoreFlowPayDownloadVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 订单流水业务层
 *
 * <AUTHOR>
 * @since 2020/11/17 7:37 下午
 */
public interface OrderFlowService extends IService<OrderFlow> {

    /**
     * 支付订单
     *
     * @param order      订单
     * @param orderItems 子订单
     */
    void payOrder(Order order, List<OrderItem> orderItems);

    /**
     * 根据售后流水退款
     *
     * @param refundFlow 售后流水
     */
    void refundOrder(RefundFlow refundFlow);

    /**
     * 根据订单退款 与上方的区别是：订单退款为全额退款，这里部分售后退款则需要根据每个orderitem项进行金额的处理
     *
     * @param refundFlow 售后流水
     * @param afterSale  子订单
     */
    void refundOrder(RefundFlow refundFlow, AfterSale afterSale);

    /**
     * 获取商家流水
     *
     * @param orderFlowQueryDTO 查询参数
     * @return 返回分页
     */
    Page<OrderFlow> orderFlowPage(OrderFlowQueryDTO orderFlowQueryDTO);

    /**
     * 根据参数查询一条数据
     *
     * @param orderFlowQueryDTO 查询参数
     * @return 返回分页
     */
    OrderFlow queryOne(OrderFlowQueryDTO orderFlowQueryDTO);

    /**
     * 获取结算单地入账流水
     *
     * @param orderFlowQueryDTO 查询条件
     * @return 入账流水
     */
    List<StoreFlowPayDownloadVO> getStoreFlowPayDownloadVO(OrderFlowQueryDTO orderFlowQueryDTO);

    /**
     * 获取店铺流水
     *
     * @param orderFlowQueryDTO 店铺流水查询参数
     * @return 商家流水集合
     */
    List<OrderFlow> listStoreFlow(OrderFlowQueryDTO orderFlowQueryDTO);

    /**
     * 更新订单流水核验状态
     *
     * @param id           订单id
     * @param verifyStatus 审核状态
     * @return 是否成功
     */
    boolean verifyOrderFlow(String id, String verifyStatus, String scene);

    /**
     * 获取店铺流水
     *
     * @param id 流水id
     * @return 订单流水
     */
    OrderFlow orderFlowDetail(String id);

    /**
     * 获取待核销的流水
     *
     * @param orderSn         订单编号
     * @param pageVO          分页参数
     * @param orderFlowStatus 订单流水状态
     * @return 订单流水
     */
    Page<OrderFlow> waitVerify(PageVO pageVO, String orderSn, String orderFlowStatus);

    /**
     * 根据订单编号获取流水
     *
     * @param orderSn 订单编号
     * @return 流水集合
     */
    List<OrderFlow> listByOrderSn(String orderSn);

    /**
     * 审核通过
     *
     * @param id           流水id
     */
    void pass(String id);

    /**
     * 审核拒绝
     *
     * @param id 流水id
     * @return 流水
     */
    OrderFlow refuse(String id);

    /**
     * 强制核销流水
     *
     * @param orderSn 订单编号
     */
    void force(String orderSn);


    /**
     * 根据订单号码查询流水
     *
     * @param sn 订单号码
     * @return 流水
     */
    OrderFlow getByOrderSn(String sn);


    /**
     * 待分账流水
     *
     * @return
     */
    List<OrderFlow> waitProfitSharingFlows();

    /**
     * 更新分账状态
     */
    void updateProfitSharingStatus();

    /**
     * 分账
     *
     * @param orderFlow
     */
    void profitSharingOrderFlow(OrderFlow orderFlow);

    /**
     * 修改订单流水状态
     * @param orderSn 订单编号
     * @param status 状态
     */
    void updateOrderFlowStatus(String orderSn,String status);


    /**
     * 根据查询参数获取子订单流水
     *
     * @param distributionOrderSearchParams 查询参数
     * @return 子订单流水
     */
    Page<DistributionOrder> queryDistributionOrder(DistributionOrderSearchParams distributionOrderSearchParams);

    /**
     * 根据查询参数获取分销金额总计
     *
     * @return 分销金额总计
     */
    DistributionDTO distributionStatistics();
}