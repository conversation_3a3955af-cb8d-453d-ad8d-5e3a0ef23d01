package plus.qdt.modules.order.order.task.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import plus.qdt.modules.order.order.task.enums.ApiUrlEnum;
import plus.qdt.modules.order.order.task.enums.DomainEnum;
import plus.qdt.modules.order.order.task.enums.FaultEnum;
import plus.qdt.modules.order.order.task.exception.ExceptionBase;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

public class RequestUtil {

    public static final String APP_ID = "517405-c871MPXeZHphayzG";
    public static final String SECRET = "mUi0q81coX4koDmEo14LO5JmT81b1mSx";
    private static final DomainEnum domain = DomainEnum.PRODUCTION;

    /**
     * 初始化快递数据
     */
    public RequestUtil() {

    }

    /**
     * 创建订单
     * @param params
     * @param domain
     * @return
     */
    public static JSONObject createOrder(Map<String, Object> params, DomainEnum domain) {
        return requestApi(ApiUrlEnum.CREATE_ORDER.getUrl(), params, domain);
    }

    /**
     * 订单变更通知
     * @param params
     * @param domain
     * @return
     */
    public static JSONObject queryOrderChangeEvent(Map<String, Object> params, DomainEnum domain) {
        return requestApi(ApiUrlEnum.QUERY_ORDER_CHANGE_EVENT.getUrl(), params, domain);
    }

    /**
     * 请求接口
     * @param url
     * @param params
     * @param domain
     * @return
     */
    private static JSONObject requestApi(String url, Map<String, Object> params, DomainEnum domain) {
        if (params == null) {
            throw new ExceptionBase(FaultEnum.REQUEST_PARAMETER_ERROR);
        } else {
            Object appId = params.get("appid");
            Object secret = params.get("secret");
            params.remove("secret");
            params.remove("appid");
            if (!isEmpty(appId) && !isEmpty(secret)) {
                try {
                    Map<String, Object> signMap = new HashMap();
                    signMap.put("timestamp", String.valueOf(System.currentTimeMillis()));
                    signMap.put("nonce", SignUtil.generateRandomString(16));
                    signMap.put("appid", appId.toString());
                    signMap.put("signature", SignUtil.signatureInvalid(signMap, secret.toString()));
                    url = MessageFormat.format(url + "?signature={0}&nonce={1}&timestamp={2}&appid={3}", signMap.get("signature"), signMap.get("nonce"), signMap.get("timestamp"), signMap.get("appid"));
                } catch (Exception var6) {
                    var6.printStackTrace();
                    throw new ExceptionBase(FaultEnum.SECURITY_EXCEPTION);
                }

                String result = HttpClientUtil.doPost(domain.getHost() + url, params);
                return JSON.parseObject(result);
            } else {
                throw new ExceptionBase(FaultEnum.MISSING_REQUEST_PARAMETERS);
            }
        }
    }

    private static boolean isEmpty(Object obj) {
        if (obj == null) {
            return true;
        } else if (obj instanceof String) {
            String str = (String) obj;
            return str.length() < 1;
        } else {
            return false;

        }
    }

    /**
     * 第三方公司查询快递代码
     * @param str
     * @return
     */
    public static String getStringMap(String str){
        Map<String,String> stringMap = new HashMap<>();
        stringMap.put("京东快递","1364825870564327424");//京东
        stringMap.put("众邮快递","1898925235405295617");//众邮快递
        stringMap.put("壹米滴答","1898925491358502913");//壹米滴答
        stringMap.put("百世汇通","1364825461946843136");//百世汇通
        stringMap.put("德邦快递/物流","1364825959177388032");//德邦
        stringMap.put("凯峰物流","1898925835534700546");//凯峰物流
        stringMap.put("中通物流","1898925997829099521");//中通物流
        stringMap.put("顺丰快递","1898926103567503362");//顺丰
        stringMap.put("速腾物流","1898926169975918593");//速腾物流
        stringMap.put("申通快递","1364825703807188992");//申通快递
        stringMap.put("圆通速递","1364825604276355072");//圆通
        stringMap.put("韵达快递","1364825661964812288");//韵达
        stringMap.put("中通快递","1425472379076792321");//中通快递
        stringMap.put("安能物流","1902916870214311938");//安能物流
        stringMap.put("百世快运","1902919923390799873");//
        stringMap.put("苏宁物流","1902919999232204802");
        stringMap.put("速必达物流","1902920119277379585");
        stringMap.put("京广速递","1902920216883027969");
        stringMap.put("顺心捷达","1902920309258379266");
        stringMap.put("优速快递","1902920366229610498");
        stringMap.put("邮政快递","1902920426652753922");
        stringMap.put("极兔速递","1902920499147104258");
        stringMap.put("邮政EMS","1364825783545102336");
        stringMap.put("丰网速运","1902920634010755074");
        return stringMap.get(str);
    }

}
