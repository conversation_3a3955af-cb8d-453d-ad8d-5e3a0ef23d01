package plus.qdt.modules.order.cart.render;

import plus.qdt.cache.Cache;
import plus.qdt.common.enums.ResultCode;
import plus.qdt.common.exception.ServiceException;
import plus.qdt.common.security.AuthUser;
import plus.qdt.common.security.context.UserContext;
import plus.qdt.modules.goods.entity.enums.GoodsTypeEnum;
import plus.qdt.modules.member.client.UserAddressClient;
import plus.qdt.modules.order.cart.entity.dto.TradeDTO;
import plus.qdt.modules.order.cart.entity.enums.CartSceneEnum;
import plus.qdt.modules.order.cart.entity.enums.RenderStepEnums;
import plus.qdt.modules.order.cart.entity.vo.CartSkuVO;
import plus.qdt.modules.order.cart.entity.vo.TradeParams;
import plus.qdt.modules.order.order.entity.dos.Order;
import plus.qdt.modules.order.order.entity.dos.Trade;
import plus.qdt.modules.order.order.service.OrderService;
import plus.qdt.modules.order.order.service.TradeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 交易构造&&创建
 *
 * <AUTHOR>
 * @since 2020-04-01 9:47 下午
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TradeBuilder {

    /**
     * 购物车渲染步骤
     */
    private final List<CartRenderStep> cartRenderSteps;
    /**
     * 交易
     */
    private final TradeService tradeService;
    /**
     * 交易
     */
    private final OrderService orderService;


    private final UserAddressClient userAddressClient;
    private final Cache<Object> cache;

    /**
     * 构造购物车
     * 购物车与结算信息不一致的地方主要是优惠券计算和运费计算，其他规则都是一致都
     *
     * @param checkedWay 购物车类型
     * @return 购物车展示信息
     */
    public TradeDTO buildCart(CartSceneEnum checkedWay) {
        //读取对应购物车的商品信息
        TradeDTO tradeDTO = this.readDTO(checkedWay);

        //购物车需要将交易中的优惠券取消掉
        if (checkedWay.equals(CartSceneEnum.CART)) {
            tradeDTO.setStoreCoupons(null);
            tradeDTO.setPlatformCoupon(null);
        }

        //按照计划进行渲染
        renderCartBySteps(tradeDTO, RenderStepStatement.cartRender);
        return tradeDTO;
    }

    /**
     * 构造结算页面
     */
    public TradeDTO buildChecked(CartSceneEnum checkedWay) {
        //读取对应购物车的商品信息
        TradeDTO tradeDTO = this.readDTO(checkedWay);
        //需要对购物车渲染
        renderCartBySteps(tradeDTO, RenderStepStatement.checkedRender);

        return tradeDTO;
    }

    /**
     * 创建一笔交易
     * 1.构造交易
     * 2.创建交易
     *
     * @param tradeDTO 交易模型
     * @return 交易信息
     */
    public Trade createTrade(TradeDTO tradeDTO) {
        if (GoodsTypeEnum.VIRTUAL_GOODS.name().equals(tradeDTO.getCheckedSkuList().getFirst().getGoodsSku().getGoodsType())) {
            renderCartBySteps(tradeDTO, RenderStepStatement.tradeVirtualRender);
        } else {
            renderCartBySteps(tradeDTO, RenderStepStatement.tradeRender);
        }
        //添加order订单及order_item子订单并返回
        return tradeService.createTrade(tradeDTO);
    }


    /**
     * 获取订单实际支付的总金额
     *
     * @param orderSn 订单sn
     * @return 金额
     */
    public Double getPaymentTotal(String orderSn) {
        Order order = orderService.getBySn(orderSn);
        return order.getFlowPrice();
    }

    /**
     * 获取整笔交易
     *
     * @param way 购物车类型
     * @return 购物车视图
     */
    public TradeDTO readDTO(CartSceneEnum way) {
        TradeDTO tradeDTO = (TradeDTO) cache.get(this.getOriginKey(way));
        AuthUser currentUser = Objects.requireNonNull(UserContext.getCurrentUser());
        if (tradeDTO == null) {
            tradeDTO = new TradeDTO(way);
            tradeDTO.setMemberId(currentUser.getExtendId());
            tradeDTO.setMemberName(currentUser.getNickName());
        }
        if (tradeDTO.getUserAddress() == null) {
            tradeDTO.setUserAddress(this.userAddressClient.getUserDefaultAddress(tradeDTO.getMemberId()));
        }
        return tradeDTO;
    }

    /**
     * 创建交易
     * 1.获取购物车类型，不同的购物车类型有不同的订单逻辑
     * 购物车类型：购物车、立即购买、虚拟商品、拼团、积分
     * 2.校验用户的收件人信息
     * 3.设置交易的基础参数
     * 4.交易信息存储到缓存中
     * 5.创建交易
     * 6.清除购物车选择数据
     *
     * @param tradeParams 创建交易参数
     * @return 交易信息
     */
    public Trade createTrade(TradeParams tradeParams) {
        //获取购物车
        CartSceneEnum cartSceneEnum = CartSceneEnum.getCartType(tradeParams.getWay());
        TradeDTO tradeDTO = this.readDTO(cartSceneEnum);
        //设置基础属性
        tradeDTO.setClientType(tradeParams.getClient());
        tradeDTO.setStoreRemark(tradeParams.getRemark());
        tradeDTO.setParentOrderSn(tradeParams.getParentOrderSn());
        //订单无收货地址校验
        if (tradeDTO.getUserAddress() == null && !GoodsTypeEnum.VIRTUAL_GOODS.name().equals(tradeDTO.getCheckedSkuList()
                .getFirst().getGoodsSku().getGoodsType())) {
            throw new ServiceException(ResultCode.MEMBER_ADDRESS_NOT_EXIST);
        }
        tradeDTO.setAreaCode(tradeParams.getAreaCode());
        //构建交易
        Trade trade = this.createTrade(tradeDTO);
        //获取订单SN用来将优惠券存入缓存
        TradeDTO newTradeDTO = this.readDTO(cartSceneEnum);
        newTradeDTO.setSn(trade.getSn());
        this.cleanChecked(newTradeDTO);
        return trade;
    }

    /**
     * 重新写入购物车
     *
     * @param tradeDTO 购物车构建器最终要构建的成品
     */
    public void resetTradeDTO(TradeDTO tradeDTO) {
        cache.put(this.getOriginKey(tradeDTO.getCartSceneEnum()), tradeDTO);
    }

    /**
     * 清空购物车
     *
     * @param way 购物车类型
     */
    public void clean(String way) {
        cache.remove(this.getOriginKey(CartSceneEnum.getCartType(way)));
    }

    /**
     * 清空购物车已选择的商品
     *
     * @param tradeDTO 购物车
     */
    private void cleanChecked(TradeDTO tradeDTO) {
        List<CartSkuVO> cartSkuVOS = tradeDTO.getSkuList();
        List<CartSkuVO> deleteVos = new ArrayList<>();
        for (CartSkuVO cartSkuVO : cartSkuVOS) {
            if (Boolean.TRUE.equals(cartSkuVO.getChecked())) {
                deleteVos.add(cartSkuVO);
            }
        }
        cartSkuVOS.removeAll(deleteVos);
        //清除添加过的备注
        tradeDTO.setNeedReceipt(false);
        tradeDTO.setReceiptVO(null);
        tradeDTO.setStoreRemark(null);
        cache.put(this.getOriginKey(tradeDTO.getCartSceneEnum()), tradeDTO);
    }


    /**
     * 读取当前会员购物原始数据key
     *
     * @param cartSceneEnum 获取方式
     * @return 当前会员购物原始数据key
     */
    private String getOriginKey(CartSceneEnum cartSceneEnum) {

        //缓存key，默认使用购物车
        if (cartSceneEnum != null) {
            AuthUser currentUser = UserContext.getCurrentExistUser();
            return cartSceneEnum.getPrefix() + currentUser.getExtendId();
        }
        throw new ServiceException();
    }


    /**
     * 根据渲染步骤，渲染购物车信息
     *
     * @param tradeDTO      交易DTO
     * @param defaultRender 渲染枚举
     */
    private void renderCartBySteps(TradeDTO tradeDTO, RenderStepEnums[] defaultRender) {
        for (RenderStepEnums step : defaultRender) {
            for (CartRenderStep render : cartRenderSteps) {
                try {
                    if (render.step().equals(step)) {
                        render.render(tradeDTO);
                    }
                } catch (ServiceException e) {
                    throw e;
                } catch (Exception e) {
                    log.error("购物车{}渲染异常：", render.getClass(), e);
                }
            }
        }
    }
}
