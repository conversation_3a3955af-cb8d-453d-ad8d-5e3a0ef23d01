package plus.qdt.modules.order.order.serviceimpl;

import plus.qdt.modules.order.order.entity.dos.FlowReviewLog;
import plus.qdt.modules.order.order.entity.dto.FlowReviewLogQueryDTO;
import plus.qdt.modules.order.order.mapper.FlowReviewLogMapper;
import plus.qdt.modules.order.order.service.FlowReviewLogService;
import plus.qdt.mybatis.util.PageUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 异常流水日志业务层
 *
 * <AUTHOR>
 * @since 2020-02-25 14:10:16
 */
@Service
public class FlowReviewLogServiceImpl extends ServiceImpl<FlowReviewLogMapper, FlowReviewLog> implements FlowReviewLogService {

    @Override
    public Page<FlowReviewLog> getReviewLogs(FlowReviewLogQueryDTO flowReviewLogQueryDTO) {
        return this.page(PageUtil.initPage(flowReviewLogQueryDTO), flowReviewLogQueryDTO.generatorQueryWrapper());
    }
}