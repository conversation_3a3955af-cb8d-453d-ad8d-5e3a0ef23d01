package plus.qdt.modules.order.order.serviceimpl;

import plus.qdt.common.enums.ResultCode;
import plus.qdt.common.exception.ServiceException;
import plus.qdt.common.security.context.UserContext;
import plus.qdt.common.security.enums.ObjectFieldEnum;
import plus.qdt.common.security.enums.SceneEnums;
import plus.qdt.common.vo.PageVO;
import plus.qdt.modules.order.order.entity.dos.Receipt;
import plus.qdt.modules.order.order.entity.dto.OrderReceiptDTO;
import plus.qdt.modules.order.order.entity.dto.ReceiptSearchParams;
import plus.qdt.modules.order.order.mapper.ReceiptMapper;
import plus.qdt.modules.order.order.service.ReceiptService;
import plus.qdt.mybatis.util.PageUtil;
import plus.qdt.mybatis.util.SceneHelp;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 发票业务层实现
 *
 * <AUTHOR>
 * @since 2020/11/17 7:38 下午
 */
@Service
public class ReceiptServiceImpl extends ServiceImpl<ReceiptMapper, Receipt> implements ReceiptService {

    @Override
    public Page<OrderReceiptDTO> getReceiptData(ReceiptSearchParams searchParams, PageVO pageVO) {
        if (UserContext.getCurrentUser().getScene().equals(SceneEnums.MEMBER)) {
            searchParams.setMemberId(UserContext.getCurrentUser().getExtendId());
        } else if (UserContext.getCurrentUser().getScene().equals(SceneEnums.STORE) || UserContext.getCurrentUser().getScene().equals(SceneEnums.SUPPLIER)) {
            searchParams.setStoreId(UserContext.getCurrentUser().getExtendId());
        }
        return this.baseMapper.getReceipt(PageUtil.initPage(pageVO), searchParams.wrapper());
    }

    @Override
    public Receipt getByOrderSn(String orderSn) {
        LambdaQueryWrapper<Receipt> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(Receipt::getOrderSn, orderSn);
        return this.getOne(lambdaQueryWrapper);
    }

    @Override
    public Receipt getDetail(String id) {
        return this.getById(id);
    }

    @Override
    public Receipt saveReceipt(Receipt receipt) {
        LambdaQueryWrapper<Receipt> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Receipt::getReceiptTitle, receipt.getReceiptTitle());
        queryWrapper.eq(Receipt::getMemberId, receipt.getMemberId());
        if (receipt.getId() != null) {
            queryWrapper.ne(Receipt::getId, receipt.getId());
        }
        if (this.getOne(queryWrapper) == null) {
            this.save(receipt);
            return receipt;
        }
        return null;
    }

    @Override
    public Receipt invoicing(String receiptId) {

        //根据id查询发票信息
        Receipt receipt = this.getById(receiptId);
        SceneHelp.objectAuthentication(receipt, ObjectFieldEnum.STORE_ID);
        if (receipt != null) {
            receipt.setReceiptStatus(1);
            this.saveOrUpdate(receipt);
            return receipt;
        }
        throw new ServiceException(ResultCode.USER_RECEIPT_NOT_EXIST);
    }
}