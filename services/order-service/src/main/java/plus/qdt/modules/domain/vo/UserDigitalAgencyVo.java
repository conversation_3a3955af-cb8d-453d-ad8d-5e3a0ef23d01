package plus.qdt.modules.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserDigitalAgencyVo {

    @Schema(description = "tab唯一键")
    private String key;

    @Schema(description = "tab名称")
    private String label;

    @Schema(title = "tab值", description = "用户代理列表")
    private List<UserDigitalAgencyVo> value;

    public UserDigitalAgencyVo(String key, String label) {
        this.key = key;
        this.label = label;
    }
}
