package plus.qdt.modules.order.order.serviceimpl;

import plus.qdt.common.utils.DateUtil;
import plus.qdt.common.utils.StringUtils;
import plus.qdt.common.vo.PageVO;
import plus.qdt.modules.order.order.entity.dos.OrderFlow;
import plus.qdt.modules.order.order.mapper.RefundOrderStatisticsMapper;
import plus.qdt.modules.order.order.service.RefundOrderStatisticsService;
import plus.qdt.modules.statistics.entity.dto.StatisticsQueryParam;
import plus.qdt.modules.statistics.entity.enums.TimeTypeEnum;
import plus.qdt.modules.statistics.entity.vo.RefundOrderStatisticsDataVO;
import plus.qdt.mybatis.util.PageUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 退款订单统计业务层实现
 *
 * <AUTHOR>
 * @since 2020/12/10 11:30
 */
@Service
public class RefundOrderStatisticsServiceImpl extends ServiceImpl<RefundOrderStatisticsMapper, OrderFlow> implements RefundOrderStatisticsService {

    @Override
    public Page<RefundOrderStatisticsDataVO> getRefundOrderStatisticsData(PageVO pageVO,
                                                                          StatisticsQueryParam statisticsQueryParam) {
        QueryWrapper queryWrapper = getQueryWrapper(statisticsQueryParam);
        return this.baseMapper.getRefundStatisticsData(PageUtil.initPage(pageVO), queryWrapper);
    }

    @Override
    public Double getRefundOrderStatisticsPrice(StatisticsQueryParam statisticsQueryParam) {

        QueryWrapper queryWrapper = getQueryWrapper(statisticsQueryParam);
        queryWrapper.select("SUM(refund_price) AS price");
        return (Double) this.getMap(queryWrapper).get("price");
    }


    private QueryWrapper getQueryWrapper(StatisticsQueryParam statisticsQueryParam) {

        QueryWrapper queryWrapper = Wrappers.query();

        //判断搜索类型是：年、月
        if (statisticsQueryParam.getTimeType().equals(TimeTypeEnum.MONTH.name())) {
            queryWrapper.between("create_time", DateUtil.getBeginTime(statisticsQueryParam.getYear(),
                    statisticsQueryParam.getMonth()), DateUtil.getEndTime(statisticsQueryParam.getYear(),
                    statisticsQueryParam.getMonth()));
        } else {
            queryWrapper.between("create_time", DateUtil.getBeginTime(statisticsQueryParam.getYear(), 1),
                    DateUtil.getEndTime(statisticsQueryParam.getYear(), 12));
        }

        //设置店铺ID
        queryWrapper.eq(!StringUtils.isEmpty(statisticsQueryParam.getStoreId()), "store_id",
                statisticsQueryParam.getStoreId());

        queryWrapper.eq("is_pay", true);
        return queryWrapper;
    }
}
