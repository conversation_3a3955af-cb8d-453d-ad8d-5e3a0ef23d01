package plus.qdt.modules.order.cart.render.impl;

import cn.hutool.core.text.CharSequenceUtil;
import plus.qdt.common.utils.CurrencyUtil;
import plus.qdt.modules.goods.client.GoodsSkuClient;
import plus.qdt.modules.goods.entity.dos.GoodsSku;
import plus.qdt.modules.order.cart.entity.dto.TradeDTO;
import plus.qdt.modules.order.cart.entity.enums.RenderStepEnums;
import plus.qdt.modules.order.cart.entity.vo.CartSkuVO;
import plus.qdt.modules.order.cart.render.CartRenderStep;
import plus.qdt.modules.order.order.entity.dto.PriceDetailDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 供应商代发逻辑处理
 *
 * <AUTHOR> @ gmail.com)
 * @version v4.0
 * @Description:
 * @since 2022/10/13 11:03
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SupplierOrderRender implements CartRenderStep {

    /**
     * 商品分类
     */
    private final GoodsSkuClient goodsSkuClient;

    @Override
    public RenderStepEnums step() {
        return RenderStepEnums.SUPPLIER;
    }

    @Override
    public void render(TradeDTO tradeDTO) {

        for (CartSkuVO cartSkuVO : tradeDTO.getSkuList()) {

            //商品是供应商商品，则对供应商加个进行处理
            if (CharSequenceUtil.isNotEmpty(cartSkuVO.getSupplierId()) && !cartSkuVO.getSupplierId().equals("-1")) {
                PriceDetailDTO priceDetailDTO = cartSkuVO.getPriceDetailDTO();

                GoodsSku supplierGoodsSku = goodsSkuClient.getGoodsSkuByIdFromCache(cartSkuVO.getGoodsSku().getSupplierSkuId());

                //目前普通上坡分类与供应商商品分类一致，所以这里不做处理
                //分销商佣金根据分类计算
//                String categoryId = cartSkuVO.getGoodsSku().getCategoryPath()
//                        .substring(cartSkuVO.getGoodsSku().getCategoryPath().lastIndexOf(",") + 1);
//
//                if (CharSequenceUtil.isNotEmpty(categoryId)) {
//                    Double commissionRate = categoryClient.getById(categoryId).getCommissionRate();
//                    //设置供应商佣金
//                    priceDetailDTO.setSupplierCommissionPoint(commissionRate);
//                }
                //设置供应商商品金额
                priceDetailDTO.setSupplierGoodsPrice(
                        CurrencyUtil.mul(supplierGoodsSku.getPrice(), cartSkuVO.getNum())
                );
            }


        }


    }
}
