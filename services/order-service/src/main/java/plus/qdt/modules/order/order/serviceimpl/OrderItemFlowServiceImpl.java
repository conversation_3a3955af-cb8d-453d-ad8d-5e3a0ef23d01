package plus.qdt.modules.order.order.serviceimpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import plus.qdt.modules.order.order.entity.dos.OrderFlow;
import plus.qdt.modules.order.order.entity.dos.OrderItem;
import plus.qdt.modules.order.order.entity.dos.OrderItemFlow;
import plus.qdt.modules.order.order.entity.dto.OrderItemFlowSearchParams;
import plus.qdt.modules.order.order.entity.vo.OrderItemFlowUserVO;
import plus.qdt.modules.order.order.mapper.OrderItemFlowMapper;
import plus.qdt.modules.order.order.service.OrderItemFlowService;
import plus.qdt.mybatis.util.PageUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * 子单流水业务
 *
 * <AUTHOR> @ gmail.com)
 * @version v4.0
 * @since 2023/7/4 18:58
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderItemFlowServiceImpl extends ServiceImpl<OrderItemFlowMapper, OrderItemFlow> implements OrderItemFlowService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateOrderItemFlow(List<OrderItem> orderItems, OrderFlow orderFlow) {
        List<OrderItemFlow> orderItemFlows = new ArrayList<>(orderItems.size());
        for (OrderItem orderItem : orderItems) {
            orderItemFlows.add(new OrderItemFlow(orderFlow, orderItem));
        }

        this.saveBatch(orderItemFlows);

    }

    @Override
    public OrderItemFlow getByOrderSnAndSkuId(String orderSn, String skuId) {
        LambdaQueryWrapper<OrderItemFlow> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderItemFlow::getOrderSn, orderSn);
        queryWrapper.eq(OrderItemFlow::getSkuId, skuId);
        return this.getOne(queryWrapper);
    }

    @Override
    public Page<OrderItemFlowUserVO> getOrderFlowUserVOByCondition(OrderItemFlowSearchParams searchParams) {
        return this.baseMapper.getOrderFlowUserVOByCondition(PageUtil.initPage(searchParams), searchParams.getQueryWrapper());
    }

    @Override
    public Long amountOderFlowUserVOByCondition(OrderItemFlowSearchParams searchParams) {
        Long amount = this.baseMapper.amountOderFlowUserVOByCondition(searchParams.getQueryWrapper());
        return amount == null ? 0L : amount;

    }
}
