package plus.qdt.modules.order.logistics.strategy.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import plus.qdt.common.exception.ServiceException;
import plus.qdt.common.utils.StringUtils;
import plus.qdt.modules.order.logistics.strategy.LogisticsQueryStrategy;
import plus.qdt.modules.order.order.entity.dos.Order;
import plus.qdt.modules.order.order.entity.dos.OrderItem;
import plus.qdt.modules.order.order.utils.ThirdPartyOrderUtils;
import plus.qdt.modules.system.client.LogisticsClient;
import plus.qdt.modules.system.entity.vo.Traces;

import java.util.List;

/**
 * 系统物流查询策略
 * 处理系统自有商品的物流查询逻辑
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Slf4j
@Component
public class SystemLogisticsQueryStrategy implements LogisticsQueryStrategy {

    @Autowired
    private LogisticsClient logisticsClient;

    @Override
    public Traces queryLogistics(Order order, List<OrderItem> orderItems) {
        log.info("使用系统物流查询策略查询物流信息，订单号: {}", order.getSn());

        // 获取收件人手机号
        String consigneeMobile = order.getConsigneeMobile();
        if (StringUtils.isBlank(consigneeMobile)) {
            throw new ServiceException("收件人手机号为空");
        }

        // 调用系统物流查询接口
        String customerName = consigneeMobile.substring(consigneeMobile.length() - 4);
        Traces traces = logisticsClient.getLogistic(order.getLogisticsCode(), order.getLogisticsNo(), customerName);

        log.info("系统物流查询完成，订单号: {}, 物流公司: {}, 物流单号: {}", 
                order.getSn(), traces != null ? traces.getShipper() : "未知", 
                traces != null ? traces.getLogisticCode() : "未知");

        return traces;
    }

    @Override
    public boolean supports(Order order, List<OrderItem> orderItems) {
        // 如果订单项中不包含三方商品，则使用系统物流查询
        boolean hasThirdPartyItems = ThirdPartyOrderUtils.hasThirdPartyItems(orderItems);
        
        log.debug("系统物流查询策略支持检查，订单号: {}, 是否包含三方商品: {}, 支持: {}", 
                order.getSn(), hasThirdPartyItems, !hasThirdPartyItems);
        
        return !hasThirdPartyItems;
    }

    @Override
    public int getPriority() {
        return 100; // 系统物流查询优先级较低，作为默认策略
    }
}
