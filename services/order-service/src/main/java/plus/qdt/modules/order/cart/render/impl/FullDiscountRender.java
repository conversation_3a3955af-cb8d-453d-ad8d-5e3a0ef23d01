package plus.qdt.modules.order.cart.render.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import plus.qdt.common.enums.PromotionTypeEnum;
import plus.qdt.common.utils.CurrencyUtil;
import plus.qdt.modules.goods.client.GoodsClient;
import plus.qdt.modules.goods.entity.dos.GoodsSku;
import plus.qdt.modules.order.cart.entity.dto.TradeDTO;
import plus.qdt.modules.order.cart.entity.dto.TradeSkuDetail;
import plus.qdt.modules.order.cart.entity.enums.RenderStepEnums;
import plus.qdt.modules.order.cart.entity.vo.CartSkuVO;
import plus.qdt.modules.order.cart.entity.vo.CartVO;
import plus.qdt.modules.order.cart.entity.vo.FullDiscountVO;
import plus.qdt.modules.order.cart.render.CartRenderStep;
import plus.qdt.modules.order.cart.render.util.PromotionPriceUtil;
import plus.qdt.modules.order.order.entity.dto.PriceDetailDTO;
import plus.qdt.modules.promotion.entity.dos.FullDiscount;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * FullDiscountRender
 *
 * <AUTHOR>
 * @since 2020-04-01 10:27 上午
 */
@Service
@RequiredArgsConstructor
public class FullDiscountRender implements CartRenderStep {


    private final GoodsClient goodsClient;

    @Override
    public RenderStepEnums step() {
        return RenderStepEnums.FULL_DISCOUNT;
    }

    @Override
    public void render(TradeDTO tradeDTO) {

        //店铺集合
        List<CartVO> cartList = tradeDTO.getCartList();

        // 如果是积分商品，则跳过
        if (PromotionTypeEnum.POINTS_GOODS.equals(tradeDTO.getPromotionType())) {
            return;
        }

        //循环店铺购物车
        for (CartVO cart : cartList) {
            //满减活动
            List<CartSkuVO> fullDiscountSkuList = cart.getSkuList().stream()
                    .filter(i -> i.getPromotionMap() != null && !i.getPromotionMap().isEmpty() && i.getPromotionMap().keySet().stream().anyMatch(j -> j.contains(PromotionTypeEnum.FULL_DISCOUNT.name())))
                    .toList();

            if (fullDiscountSkuList.isEmpty()) {
                continue;
            }
            //获取满减活动
            Optional<Map.Entry<String, Object>> fullDiscountOptional =
                    fullDiscountSkuList.get(0).getPromotionMap().entrySet().stream().filter(i -> i.getKey().contains(PromotionTypeEnum.FULL_DISCOUNT.name())).findFirst();

            //如果没有满减活动，则跳过
            if (fullDiscountOptional.isPresent()) {
                JSONObject promotionsObj = JSONUtil.parseObj(fullDiscountOptional.get().getValue());
                FullDiscount fullDiscount = promotionsObj.toBean(FullDiscount.class);
                FullDiscountVO fullDiscountVO = new FullDiscountVO(fullDiscount);

                //如果有赠品，则将赠品信息写入
                if (Boolean.TRUE.equals(fullDiscount.getGiftFlag())) {
                    GoodsSku goodsSku = goodsClient.getGoodsSkuByIdFromCache(fullDiscount.getGiftId());
                    if (goodsSku != null) {
                        fullDiscountVO.setGiftSkuId(fullDiscount.getGiftId());
                        fullDiscountVO.setGiftSkuName(goodsSku.getGoodsName());
                    }
                }

                //写入满减活动
                cart.setFullDiscount(fullDiscountVO);
                List<TradeSkuDetail> skuPriceDetail = new ArrayList<>();
                for (CartSkuVO cartSkuVO : cart.getCheckedSkuList()) {
                    skuPriceDetail.add(new TradeSkuDetail(cartSkuVO.getGoodsSku().getId(), cartSkuVO.getPriceDetailDTO().getGoodsPrice()));
                }
                //记录参与满减活动的sku
                cart.setFullDiscountSkuIds(skuPriceDetail.stream().map(TradeSkuDetail::getId).toList());

                //判定满减金额
                Double countPrice = countPrice(skuPriceDetail);


                if (isFull(countPrice, cart)) {
                    //如果减现金
                    if (Boolean.TRUE.equals(fullDiscount.getFullMinusFlag())) {
                        this.renderFullDiscount(cart, skuPriceDetail, fullDiscount.getFullMinus());
                    }
                    //打折
                    else if (Boolean.TRUE.equals(fullDiscount.getFullRateFlag())) {
                        this.renderFullRate(cart, skuPriceDetail, CurrencyUtil.div(fullDiscount.getFullRate(), 10));
                    }
                    //渲染满优惠
                    renderFullMinus(cart);
                }
            }

        }

    }

    /**
     * 渲染满减
     *
     * @param cart           购物车展示VO
     * @param skuPriceDetail sku价格详细
     */
    private void renderFullDiscount(CartVO cart, List<TradeSkuDetail> skuPriceDetail, Double discountPrice) {


        //分配佣金金额
        Map<String, Double> promotionPriceMap = PromotionPriceUtil.recountPrice(skuPriceDetail, discountPrice);


        //循环计算扣减金额
        promotionPriceMap.forEach((skuId, price) -> cart.getCheckedSkuList().stream().filter(cartSkuVO -> cartSkuVO.getGoodsSku().getId().equals(skuId)).findFirst().ifPresent(cartSkuVO -> {
            PriceDetailDTO priceDetailDTO = cartSkuVO.getPriceDetailDTO();
            priceDetailDTO.setDiscountPrice(price);
        }));

    }

    /**
     * 渲染满折
     *
     * @param cart           购物车展示VO
     * @param skuPriceDetail sku价格详细
     */
    private void renderFullRate(CartVO cart, List<TradeSkuDetail> skuPriceDetail, Double rate) {

        List<CartSkuVO> cartSkuVOS = cart.getCheckedSkuList().stream()
                .filter(cartSkuVO ->
                        skuPriceDetail.stream().anyMatch(i -> i.getId().equals(cartSkuVO.getGoodsSku().getId())))
                .toList();

        // 循环计算扣减金额
        cartSkuVOS.forEach(cartSkuVO -> {
            PriceDetailDTO priceDetailDTO = cartSkuVO.getPriceDetailDTO();

            //优惠金额=旧的优惠金额+商品金额*商品折扣比例
            priceDetailDTO.setDiscountPrice(
                    CurrencyUtil.add(priceDetailDTO.getDiscountPrice(),
                            CurrencyUtil.mul(priceDetailDTO.getGoodsPrice(),
                                    CurrencyUtil.sub(1, rate)
                            )
                    )
            );
        });

    }

    /**
     * 渲染满减优惠
     *
     * @param cartVO 购物车满优惠渲染
     */
    private void renderFullMinus(CartVO cartVO) {
        //获取参与活动的商品总价
        FullDiscountVO fullDiscount = cartVO.getFullDiscount();

        for (CartSkuVO skuVO : cartVO.getCheckedSkuList()) {
            //如果满足，判定是否免邮，免邮的话需要渲染一边sku
            if (Boolean.TRUE.equals(fullDiscount.getFreeFreightFlag())) {
                skuVO.setIsFreeFreight(true);
            }
            if (skuVO.getPromotionTypeEnum() == null) {
                skuVO.setPromotionTypeEnum(PromotionTypeEnum.FULL_DISCOUNT);
                skuVO.setPromotionId(fullDiscount.getId());
            }
        }

        if (Boolean.TRUE.equals(fullDiscount.getCouponFlag())) {
            cartVO.getGiftCouponList().add(fullDiscount.getCouponId());
        }
        if (Boolean.TRUE.equals(fullDiscount.getGiftFlag())) {
            cartVO.setGiftList(Arrays.asList(fullDiscount.getGiftId().split(",")));
        }
        if (Boolean.TRUE.equals(fullDiscount.getPointFlag())) {
            cartVO.setGiftPoint(fullDiscount.getPoints());
        }

    }


    /**
     * 是否满足满优惠
     *
     * @param cart 购物车展示信息
     * @return 是否满足满优惠
     */
    private boolean isFull(Double price, CartVO cart) {
        if (cart.getFullDiscount().getFullMoney() <= price) {
            cart.setPromotionNotice("正在参与满优惠活动[" + cart.getFullDiscount().getPromotionName() + "]" + cart.getFullDiscount().notice());
            return true;
        } else {
            cart.setPromotionNotice("还差" + CurrencyUtil.sub(cart.getFullDiscount().getFullMoney(), price) + " 即可参与活动（" + cart.getFullDiscount().getPromotionName() + "）" + cart.getFullDiscount().notice());
            return false;
        }
    }

    /**
     * 统计参与满减商品价格
     *
     * @param skuPriceMap sku价格
     * @return 总价
     */
    private Double countPrice(List<TradeSkuDetail> skuPriceMap) {
        double count = 0d;

        for (TradeSkuDetail price : skuPriceMap) {
            count = CurrencyUtil.add(count, price.getAmount());
        }

        return count;
    }
}
