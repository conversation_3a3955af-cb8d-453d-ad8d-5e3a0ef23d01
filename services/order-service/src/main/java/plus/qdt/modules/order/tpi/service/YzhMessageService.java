package plus.qdt.modules.order.tpi.service;

import plus.qdt.modules.system.entity.dto.YzhQueryMsgListRequestParam;
import plus.qdt.modules.system.entity.dto.YzhQueryMsgListResponse;
import plus.qdt.modules.system.entity.dto.YzhDeleteMsgResponse;

import java.util.List;

/**
 * 云中鹤消息服务接口
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
public interface YzhMessageService {

    /**
     * 查询消息列表
     *
     * @param requestParam 查询消息列表请求参数
     * @return 消息列表响应结果
     * <AUTHOR>
     */
    YzhQueryMsgListResponse queryMessageList(YzhQueryMsgListRequestParam requestParam);

    /**
     * 处理消息列表中的消息
     *
     * @param response 消息列表响应结果
     * <AUTHOR>
     */
    void processMessages(YzhQueryMsgListResponse response);

    /**
     * 删除已处理的消息
     *
     * @param messages 需要删除的消息列表
     * @return 删除结果
     * <AUTHOR>
     */
    YzhDeleteMsgResponse deleteProcessedMessages(List<YzhQueryMsgListResponse.YzhMessage> messages);

    /**
     * 处理商品下架消息
     *
     * @param message 消息对象
     * <AUTHOR>
     */
    void handleGoodsOffShelf(YzhQueryMsgListResponse.YzhMessage message);

    /**
     * 处理商品改价消息
     *
     * @param message 消息对象
     * <AUTHOR>
     */
    void handleGoodsPriceChange(YzhQueryMsgListResponse.YzhMessage message);

    /**
     * 处理商品停售消息
     *
     * @param message 消息对象
     * <AUTHOR>
     */
    void handleGoodsStopSale(YzhQueryMsgListResponse.YzhMessage message);

    /**
     * 处理新品上架消息
     *
     * @param message 消息对象
     * <AUTHOR>
     */
    void handleGoodsOnShelf(YzhQueryMsgListResponse.YzhMessage message);
}
