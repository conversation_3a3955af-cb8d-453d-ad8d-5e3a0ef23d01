package plus.qdt.modules.order.order.serviceimpl;

import plus.qdt.common.enums.ResultCode;
import plus.qdt.common.exception.ServiceException;
import plus.qdt.modules.order.order.entity.dos.OrderPackage;
import plus.qdt.modules.order.order.entity.dos.OrderPackageItem;
import plus.qdt.modules.order.order.entity.vo.OrderPackageVO;
import plus.qdt.modules.order.order.mapper.OrderPackageMapper;
import plus.qdt.modules.order.order.service.OrderPackageItemService;
import plus.qdt.modules.order.order.service.OrderPackageService;
import plus.qdt.modules.system.client.LogisticsClient;
import plus.qdt.modules.system.entity.vo.Traces;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 订单包裹业务层实现
 *
 * <AUTHOR>
 * @since 2020/11/17 7:38 下午
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class OrderPackageServiceImpl extends ServiceImpl<OrderPackageMapper, OrderPackage> implements OrderPackageService {

    @Autowired
    private OrderPackageItemService orderpackageItemService;

    @Autowired
    private LogisticsClient logisticsClient;

    @Override
    public List<OrderPackage> getOrderPackageList(String orderSn) {
        return this.list(new LambdaQueryWrapper<OrderPackage>().eq(OrderPackage::getOrderSn, orderSn));
    }

    /**
     * 根据订单编号获取发货包裹详情
     * @param orderSn 订单编号
     * @return 发货包裹试图列表
     */
    @Override
    public List<OrderPackageVO> getOrderPackageVOList(String orderSn) {
        List<OrderPackage> orderPackages = this.getOrderPackageList(orderSn);
        if (orderPackages == null){
            throw new ServiceException(ResultCode.ORDER_PACKAGE_NOT_EXIST);
        }
        List<OrderPackageVO> orderPackageVOS = new ArrayList<>();
        orderPackages.forEach(orderPackage -> {
            OrderPackageVO orderPackageVO = new OrderPackageVO(orderPackage);
            // 获取子订单包裹详情
            List<OrderPackageItem> orderPackageItemList = orderpackageItemService.getOrderPackageItemListByPno(orderPackage.getPackageNo());
            orderPackageVO.setOrderPackageItemList(orderPackageItemList);
            String str = orderPackage.getConsigneeMobile();
            str = str.substring(str.length() - 4);
            Traces traces = logisticsClient.getLogistic(orderPackage.getLogisticsCode(), orderPackage.getLogisticsNo(), str);
            orderPackageVO.setTraces(traces);
            orderPackageVOS.add(orderPackageVO);
        });

        return orderPackageVOS;
    }
}