package plus.qdt.modules.order.order.task;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import plus.qdt.modules.order.order.entity.dos.Order;
import plus.qdt.modules.order.order.mapper.OrderMapper;
import plus.qdt.modules.order.order.task.enums.DomainEnum;
import plus.qdt.modules.order.order.task.utils.RequestUtil;
import plus.qdt.modules.order.order.thirdparty.ysy.YsyOrderInfo;
import plus.qdt.modules.payment.client.PaymentClient;
import plus.qdt.modules.payment.entity.dto.CashierParam;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 订单变化定时
 */
@Slf4j
@Component
public class OrderChangeEventTask {

    @Autowired
    private OrderMapper orderMapper;
    private final PaymentClient paymentClient;

    public static final String APP_ID = "517405-c871MPXeZHphayzG";

    public static final String SECRET = "mUi0q81coX4koDmEo14LO5JmT81b1mSx";
    private final DomainEnum domain = DomainEnum.PRODUCTION;

    public OrderChangeEventTask(PaymentClient paymentClient) {
        this.paymentClient = paymentClient;
    }

    /**
     * 订单变动事件
     */
    @XxlJob("orderChangeEvent")
    public void orderChangeEvent() {
        //查询订单变动事件
        Map<String, Object> map = new HashMap<>();
        map.put("appid", APP_ID);
        map.put("secret", SECRET);
        JSONObject jsonObject = RequestUtil.queryOrderChangeEvent(map, domain);
        String msg = jsonObject.getString("msg");
        JSONArray result = jsonObject.getJSONArray("result");
        if("SUCCESS".equals(msg) && result.size()>0){
            JSONObject resultJSONObject = result.getJSONObject(0);
            JSONArray changesContent = resultJSONObject.getJSONArray("changesContent");
            for (int i=0;i<changesContent.size();i++){
                //修改订单物流信息
                JSONObject contentJSONObject = changesContent.getJSONObject(i);
                //第三方平台订单号
                String unionNo = contentJSONObject.getString("unionNo");
                //物流信息
                JSONArray orderDeliveredList = contentJSONObject.getJSONArray("orderDeliveredList");
                for (int j=0;i<orderDeliveredList.size();i++){
                    JSONObject content = orderDeliveredList.getJSONObject(0);
                    String expNo = content.getString("expNo");
                    String expName = content.getString("expName");
                    //查询快递
                    YsyOrderInfo ysyOrderInfo = paymentClient.selectOrder(unionNo);
                    QueryWrapper<Order> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("sn",ysyOrderInfo.getOrderSn());
                    //本地订单信息
                    Order order = orderMapper.selectOne(queryWrapper);
                    order.setLogisticsCode(RequestUtil.getStringMap(expName));//物流公司id
                    order.setLogisticsNo(expNo);//物流单号
                    order.setOrderStatus("DELIVERED");//修改订单状态
                    order.setField5(unionNo);//第三方平台订单号
                    orderMapper.updateById(order);
                }
            }
        }
    }

    /**
     * 商品变动事件
     */


}
