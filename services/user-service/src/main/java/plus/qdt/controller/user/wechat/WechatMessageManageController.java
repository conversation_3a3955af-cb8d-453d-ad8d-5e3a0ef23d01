package plus.qdt.controller.user.wechat;

import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.vo.PageVO;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.wechat.entity.dos.WechatMessage;
import plus.qdt.modules.wechat.service.WechatMessageService;
import plus.qdt.mybatis.util.PageUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 微信消息接口
 *
 * <AUTHOR>
 * @since 2020/12/2 10:40
 */
@RestController
@Tag(name = "微信消息接口")
@RequestMapping("/user/wechatMessage")
@RequiredArgsConstructor
public class WechatMessageManageController {
    private final WechatMessageService wechatMessageService;

    @GetMapping(value = "/init")
    @Operation(summary = "初始化微信消息")
    public ResultMessage<Object> init() {
        wechatMessageService.init();
        return ResultUtil.success();
    }

    @GetMapping(value = "/{id}")
    @Operation(summary = "查看微信消息详情")
    public ResultMessage<WechatMessage> get(@PathVariable String id) {

        WechatMessage wechatMessage = wechatMessageService.getById(id);
        return ResultUtil.data(wechatMessage);
    }

    @GetMapping
    @Operation(summary = "分页获取微信消息")
    public ResultMessage<Page<WechatMessage>> getByPage(PageVO page) {
        Page<WechatMessage> data = wechatMessageService.page(PageUtil.initPage(page));
        return ResultUtil.data(data);
    }

    @PostMapping
    @Operation(summary = "新增微信消息")
    public ResultMessage<WechatMessage> save(WechatMessage wechatMessage) {

        wechatMessageService.save(wechatMessage);
        return ResultUtil.data(wechatMessage);
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新微信消息")
    public ResultMessage<WechatMessage> update(@PathVariable String id, WechatMessage wechatMessage) {
        wechatMessageService.updateById(wechatMessage);
        return ResultUtil.data(wechatMessage);
    }

    @DeleteMapping(value = "/{ids}")
    @Operation(summary = "删除微信消息")
    public ResultMessage<Object> delAllByIds(@PathVariable List ids) {
        wechatMessageService.removeByIds(ids);
        return ResultUtil.success();
    }
}
