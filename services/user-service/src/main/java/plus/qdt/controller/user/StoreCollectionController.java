package plus.qdt.controller.user;

import plus.qdt.common.enums.ResultCode;
import plus.qdt.common.exception.ServiceException;
import plus.qdt.common.security.AuthUser;
import plus.qdt.common.security.context.UserContext;
import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.vo.PageVO;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.member.service.StoreCollectionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 买家端,会员收藏接口
 *
 * <AUTHOR>
 * @since 2020/11/17 2:32 下午
 */
@RestController
@Tag(name = "买家端,会员收藏接口")
@RequestMapping("/user/collection")
@RequiredArgsConstructor
public class StoreCollectionController {

    /**
     * 会员店铺
     */
    private final StoreCollectionService storeCollectionService;

    @Operation(summary = "查询会员收藏列表")
    @GetMapping("/SHOP")
    public ResultMessage<Object> goodsList(PageVO page) {
        if (UserContext.getCurrentUser() == null) {
            throw new ServiceException(ResultCode.USER_AUTH_EXPIRED);
        }
        return ResultUtil.data(storeCollectionService.storeCollection(page));
    }

    @Operation(summary = "添加会员收藏")
    @PostMapping("/add/SHOP/{id}")
    public ResultMessage<Object> addGoodsCollection(@NotNull(message = "值不能为空") @PathVariable String id) {
        return ResultUtil.data(storeCollectionService.addStoreCollection(id));

    }

    @Operation(summary = "删除会员收藏")
    @DeleteMapping(value = "/delete/SHOP/{id}")
    public ResultMessage<Object> deleteGoodsCollection(@NotNull(message = "值不能为空") @PathVariable String id) {
        return ResultUtil.data(storeCollectionService.deleteStoreCollection(id));
    }

    @Operation(summary = "查询会员是否收藏")
    @GetMapping(value = "/isCollection/SHOP/{id}")
    public ResultMessage<Boolean> isCollection(@NotNull(message = "值不能为空") @PathVariable String id) {
        AuthUser currentUser = UserContext.getCurrentUser();
        if (currentUser == null) {
            return ResultUtil.data(false);
        }
        return ResultUtil.data(this.storeCollectionService.isCollection(id));
    }
}
