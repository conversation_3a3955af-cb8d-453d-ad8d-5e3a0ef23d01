package plus.qdt.controller.user;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import plus.qdt.common.aop.annotation.PreventDuplicateSubmissions;
import plus.qdt.common.enums.SwitchEnum;
import plus.qdt.common.security.OperationalJudgment;
import plus.qdt.common.security.enums.AuthUserFieldEnum;
import plus.qdt.common.security.enums.ObjectFieldEnum;
import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.member.entity.dos.MemberEvaluation;
import plus.qdt.modules.member.entity.dto.EvaluationQueryParams;
import plus.qdt.modules.member.entity.dto.MemberEvaluationDTO;
import plus.qdt.modules.member.entity.vo.EvaluationNumberVO;
import plus.qdt.modules.member.entity.vo.MemberEvaluationPageVO;
import plus.qdt.modules.member.entity.vo.MemberEvaluationVO;
import plus.qdt.modules.member.service.MemberEvaluationService;

/**
 * 会员商品评价接口
 *
 * <AUTHOR>
 * @since 2020-02-25 14:10:16
 */
@RestController
@Tag(name = "会员商品评价接口")
@RequestMapping("/user/evaluation")
public class UserEvaluationController {
    @Autowired
    private MemberEvaluationService memberEvaluationService;

    @PreventDuplicateSubmissions
    @Operation(summary = "通过id获取评论")
    @GetMapping(value = "/{id}")
    public ResultMessage<MemberEvaluationVO> get(@PathVariable String id) {
        return ResultUtil.data(memberEvaluationService.queryById(id));
    }

    @Operation(summary = "查看某一个商品的评价列表")
    @GetMapping(value = "/show")
    public ResultMessage<MemberEvaluationPageVO> queryGoodsEvaluation(EvaluationQueryParams evaluationQueryParams) {
        //设置查询查询商品
        evaluationQueryParams.setStatus(SwitchEnum.OPEN.name());
        return ResultUtil.data(memberEvaluationService.queryPage(evaluationQueryParams));
    }

    @Operation(summary = "查看某一个商品的评价数量")
    @GetMapping(value = "/number/{goodsId}")
    public ResultMessage<EvaluationNumberVO> queryEvaluationNumber(@NotNull @PathVariable("goodsId") String goodsId) {
        return ResultUtil.data(memberEvaluationService.getEvaluationNumber(goodsId));
    }

    @PreventDuplicateSubmissions
    @Operation(summary = "添加会员评价")
    @PostMapping
    public ResultMessage<MemberEvaluationDTO> save(@Valid @RequestBody MemberEvaluationDTO memberEvaluationDTO) {
        return ResultUtil.data(memberEvaluationService.addMemberEvaluation(memberEvaluationDTO, true));
    }

    @Operation(summary = "获取评价分页")
    @GetMapping
    public ResultMessage<MemberEvaluationPageVO> getByPage(EvaluationQueryParams evaluationQueryParams) {

        return ResultUtil.data(memberEvaluationService.queryPage(evaluationQueryParams));
    }

    @Operation(summary = "我的评价列表：店铺或者供应商，则根据当前用户信息查看")
    @GetMapping("/mine")
    public ResultMessage<Page<MemberEvaluation>> mine(EvaluationQueryParams evaluationQueryParams) {

        return ResultUtil.data(memberEvaluationService.mineEvaluation(evaluationQueryParams));
    }

    @PreventDuplicateSubmissions
    @Operation(summary = "修改评价状态")
    @GetMapping(value = "/update/status/{id}")
    public ResultMessage<Object> updateStatus(@PathVariable String id, @NotNull String status) {
        OperationalJudgment.judgment(memberEvaluationService.queryById(id));
        memberEvaluationService.updateStatus(id, status);
        return ResultUtil.success();
    }

    @Operation(summary = "删除评论")
    @DeleteMapping(value = "/{id}")
    public ResultMessage<Page<Object>> delete(@PathVariable String id) {
        OperationalJudgment.judgment(memberEvaluationService.queryById(id));
        memberEvaluationService.delete(id);
        return ResultUtil.success();
    }

    @Operation(summary = "回复评价")
    @PutMapping(value = "/reply/{id}")
    public ResultMessage<MemberEvaluationVO> reply(@PathVariable String id, @RequestParam String reply, @RequestParam String replyImage) {
        OperationalJudgment.judgment(memberEvaluationService.queryById(id), ObjectFieldEnum.STORE_ID, AuthUserFieldEnum.EXTEND_ID);
        memberEvaluationService.reply(id, reply, replyImage);
        return ResultUtil.success();
    }

}
