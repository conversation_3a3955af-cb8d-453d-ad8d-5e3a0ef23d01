package plus.qdt.controller.feign.wechat;

import plus.qdt.modules.wechat.client.WechatMPMessageClient;
import plus.qdt.modules.wechat.entity.dos.WechatMPMessage;
import plus.qdt.modules.wechat.service.WechatMPMessageService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 微信公众号消息订阅feign
 *
 * <AUTHOR>
 * @since  2023/3/22
 */
@RestController
@RequiredArgsConstructor
public class WechatMPMessageFeignController implements WechatMPMessageClient {

    public final WechatMPMessageService wechatMPMessageService;

    @Override
    public List<WechatMPMessage> list() {
        return wechatMPMessageService.list();
    }
}
