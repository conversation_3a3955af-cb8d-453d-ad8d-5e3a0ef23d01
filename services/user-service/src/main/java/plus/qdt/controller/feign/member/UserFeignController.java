package plus.qdt.controller.feign.member;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import plus.qdt.common.security.enums.SceneEnums;
import plus.qdt.common.utils.DateUtil;
import plus.qdt.modules.member.client.UserClient;
import plus.qdt.modules.member.entity.dos.User;
import plus.qdt.modules.member.entity.dto.UserInfoDTO;
import plus.qdt.modules.member.entity.dto.UserLoginDTO;
import plus.qdt.modules.member.entity.dto.UserSearchParams;
import plus.qdt.modules.member.entity.enums.PromotionGrade;
import plus.qdt.modules.member.entity.vo.UserVO;
import plus.qdt.modules.member.service.ShareLogService;
import plus.qdt.modules.member.service.UserSecurityService;
import plus.qdt.modules.member.service.UserService;
import plus.qdt.modules.share.ShareLog;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 会员 feign client
 *
 * <AUTHOR>
 * @version v1.0 2021-11-17 18:06
 */
@RestController
@RequiredArgsConstructor
public class UserFeignController implements UserClient {

    private final UserService userService;
    private final ShareLogService shareService;
    private final UserSecurityService userSecurityService;


    @Override
    public User getById(String id) {
        return userService.getById(id, false);
    }

    @Override
    public User getByMobile(String mobile, SceneEnums scene) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getMobile, mobile);
        queryWrapper.eq(User::getScene, scene.name());
        return userService.getOne(queryWrapper, false);
    }

    /**
     * 获取指定会员数据
     *
     * @param columns   指定获取的列
     * @param memberIds 会员ids
     * @return 指定会员数据
     */
    @Override
    public List<Map<String, Object>> listFieldsByMemberIds(String columns, List<String> memberIds) {
        return userService.listFieldsByCondition(columns, UserSearchParams.builder().userIds(memberIds).build());
    }

    /**
     * 获取所有会员的手机号
     *
     * @return 所有会员的手机号
     */
    @Override
    public List<String> getAllMemberMobile() {
        //        TODO 这里需要重新实现
        return userService.getMemberMobile();
    }

    /**
     * 根据条件查询会员总数
     *
     * @param userSearchParams 查询参数
     * @return 会员总数
     */
    @Override
    public long getUserNum(UserSearchParams userSearchParams) {
        return userService.getMemberNum(userSearchParams);
    }

    /**
     * 根据条件查询会员列表
     *
     * @param searchParams 查询参数
     * @return 会员列表
     */
    @Override
    public List<User> list(UserSearchParams searchParams) {
        return userService.list(searchParams);
    }

    @Override
    public List<User> list(LambdaQueryWrapper<User> searchParams) {
        return userService.list(searchParams);
    }

    /**
     * 更新会员登录时间为最新时间
     *
     * @param userId 会员id
     * @return 是否更新成功
     */
    @Override
    public Boolean updateUserLoginTime(String userId) {
        return userService.updateUserLoginTime(userId);
    }


    @Override
    public User queryUser(UserLoginDTO userLoginDTO) {
        return userService.userLoginQuery(userLoginDTO);
    }

    @Override
    public User mobilePhoneLogin(String mobile, SceneEnums scene, String promotionCode) {
        return userService.mobilePhoneLogin(mobile, scene, promotionCode);
    }

    @Override
    public User register(UserInfoDTO userInfoDTO) {
        return userService.registerHandler(userInfoDTO);
    }

    @Override
    public void resetPasswordSign(String uuid, String mobile, SceneEnums scene) {
        userSecurityService.resetPasswordSign(uuid, mobile, scene);
    }

    @Override
    public Boolean resetPasswordByMobile(String uuid, String password, SceneEnums scene) {
        return userSecurityService.resetPasswordByMobile(uuid, password, scene);
    }

    @Override
    public UserVO info() {
        return userService.userInfo();
    }

    @Override
    public void logoffDelete() {
        userService.logoffDelete();
    }

    @Override
    public void insertShareLog(String promotionCode, String userId, String name, String image) {
        userService.insertStarLog(promotionCode, userId, name, image, false);
    }

    @Override
    public void openVip(String userId) {
        // 获取当前用户是否存在vip
        User user = userService.getById(userId);
        // 开通vip
        user.setVip(true);
        // 获取vip是否过期
        if (user.getVipExpire() == null || user.getVipExpire().before(new Date())) {
            user.setVipExpire(DateUtil.offsetDay(new Date(), 365));
        } else if (user.getVipExpire().after(new Date())) {
            // 没有过期续费的则添加365天时间
            user.setVipExpire(DateUtil.offsetDay(user.getVipExpire(), 365));
        }
        userService.updateById(user);
    }

    @Override
    public List<String> listCityPartner() {
        LambdaQueryWrapper<ShareLog> lqw = new LambdaQueryWrapper<>();
        lqw.select(ShareLog::getSharedId);
        // 购买了年卡且没有过期的
        lqw.inSql(ShareLog::getSharedId, "SELECT id FROM li_user WHERE vip = true AND vip_expire > NOW()");
        lqw.eq(ShareLog::getIdentity, PromotionGrade.CITY_PARTNER.getGrade());
        return shareService.listObjs(lqw, Object::toString);
    }

    @Override
    public User getShareInfo(String memberId) {
        ShareLog share = shareService.getByUserId(memberId);
        if (share != null) {
            return userService.getById(share.getShareId(), false);
        }
        return null;
    }

    @Override
    public List<User> listCreator() {
        QueryWrapper<User> lqw = new QueryWrapper<>();
        lqw.inSql("id", String.format("SELECT shared_id FROM li_share_log WHERE identity=%s",
                PromotionGrade.GLOBAL_CO_FOUNDER.getGrade()));
        lqw.select("id", "IFNULL(real_name, nick_name) AS nick_name");
        return userService.list(lqw);
    }

    @Override
    public List<String> getVipUserId() {
        LambdaQueryWrapper<User> lqw = new LambdaQueryWrapper<>();
        lqw.eq(User::getVip, true);
        lqw.ge(User::getVipExpire, new Date());
        lqw.select(User::getId);
        return userService.listObjs(lqw, Object::toString);
    }
}
