package plus.qdt.modules.member.service;

import plus.qdt.modules.member.entity.dos.User;
import plus.qdt.modules.member.entity.dto.MemberStatisticsSearchParams;
import plus.qdt.modules.member.entity.vo.MemberDistributionVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/6/28
 **/
public interface MemberStatisticsService extends IService<User> {

    /**
     * 获取会员数量
     *
     * @return 会员统计
     */
    long getMemberCount();

    /**
     * 获取今日新增会员数量
     *
     * @return 今日新增会员数量
     */
    long todayMemberNum();

    /**
     * 获取指定结束时间前的会员数量
     *
     * @param endTime
     * @return
     */
    long memberCount(Date endTime);

    /**
     * 当天活跃会员数量
     *
     * @param startTime
     * @return
     */
    long activeQuantity(Date startTime);

    /**
     * 时间段内新增会员数量
     *
     * @param searchParams
     * @return
     */
    long newlyAdded(MemberStatisticsSearchParams searchParams);


    /**
     * 查看会员数据分布
     *
     * @return 会员数据分布
     */
    List<MemberDistributionVO> distribution();
}
