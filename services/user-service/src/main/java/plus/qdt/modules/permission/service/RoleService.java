package plus.qdt.modules.permission.service;


import plus.qdt.common.vo.PageVO;
import plus.qdt.modules.permission.entity.dos.Role;
import plus.qdt.modules.permission.entity.dto.RoleEditDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 角色业务层
 *
 * <AUTHOR>
 * @since 2020/11/17 3:45 下午
 */
public interface RoleService extends IService<Role> {

    /**
     * 获取默认角色
     *
     * @param defaultRole
     * @return
     */
    List<Role> findByDefaultRole(Boolean defaultRole);


    /**
     * 批量删除角色
     *
     * @param roleIds
     */
    void deleteRoles(List<String> roleIds);

    /**
     * 获取当前店铺角色
     */
    List<Role> findCurrentRole();

    /**
     * 保存角色
     *
     * @param role 角色
     */
    void saveRole(RoleEditDTO role);


    /**
     * 分页查询角色
     *
     * @param pageVo 分页参数
     * @param role   角色
     */
    Page pageRole(PageVO pageVo, Role role);

    /**
     * 更新角色
     *
     * @param role 角色
     */
    void updateRoleById(RoleEditDTO role);
}
