package plus.qdt.modules.member.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import plus.qdt.modules.domain.dto.UserFriendDto;
import plus.qdt.modules.domain.vo.UserInsertFriendVo;
import plus.qdt.modules.member.entity.dos.QdtUserFriend;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2.0
 */
public interface QdtUserFriendMapper extends BaseMapper<QdtUserFriend> {

    /**
     * 获取星标好友
     * @param userId 当前用户
     * @return {@link List} {@link UserFriendDto}
     * <AUTHOR>
     */
    List<UserFriendDto> selectStarList(@Param("userId") String userId);

    /**
     * 查询非星标好友列表
     * @param userId 用户ID
     * @return {@link List} {@link UserFriendDto}
     * <AUTHOR>
     */
    List<UserFriendDto> selectFriends(@Param("userId") String userId);

    /**
     * 查询非星标好友列表
     * @param userId 用户ID
     * @return {@link List} {@link UserFriendDto}
     * <AUTHOR>
     */
    List<UserFriendDto> selectOtherFriends(@Param("userId") String userId);

    /**
     * 查看消息数量
     *
     * @param userId 用户ID
     * @param size 数量大小
     * @return {@link List} {@link UserInsertFriendVo}
     * <AUTHOR>
     */
    List<QdtUserFriend> selectMessage(@Param("userId") String userId, @Param("size") Integer size);
}
