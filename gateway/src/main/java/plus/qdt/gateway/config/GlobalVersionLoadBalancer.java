package plus.qdt.gateway.config;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.nacos.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.loadbalancer.*;
import org.springframework.cloud.loadbalancer.core.ReactorServiceInstanceLoadBalancer;
import org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier;
import plus.qdt.gateway.loadbalance.factory.QdtLoadBalanceFactory;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025-06-11
 */
@Slf4j
public class GlobalVersionLoadBalancer implements ReactorServiceInstanceLoadBalancer {
    private final String serviceId;  // 当前服务名称
    private final ObjectProvider<ServiceInstanceListSupplier> supplier;

    private LoadBalancerProperties properties;

    public GlobalVersionLoadBalancer(ObjectProvider<ServiceInstanceListSupplier> supplier, String serviceId) {
        this.supplier = supplier;
        this.serviceId = serviceId;
    }

    @Override
    public Mono<Response<ServiceInstance>> choose(Request request) {
        properties = SpringUtil.getBean(LoadBalancerProperties.class);
        String targetVersion;
        Object context = request.getContext();
        if (context instanceof RequestDataContext) {
            RequestDataContext exchange = (RequestDataContext) context;
            RequestData clientRequest = exchange.getClientRequest();
            targetVersion = clientRequest.getHeaders().getFirst(properties.getHeader());
        } else {
            // 如果还有其他类型的上下文，可以继续扩展
            targetVersion = "";
            log.debug("No suitable request context found for version extraction");
        }

        return supplier
                .getIfAvailable()
                .get()
                .next()
                .map(instances -> {
                    List<ServiceInstance> healthyInstances = new ArrayList<>(instances);
                    return routeWithFallback(healthyInstances, targetVersion);
                });
    }

    /**
     * 三级路由策略：
     * 1. 优先匹配请求头指定版本
     * 2. 次选无版本标记的实例
     * 3. 兜底选择任意可用实例
     */
    private Response<ServiceInstance> routeWithFallback(List<ServiceInstance> instances, String version) {
        // 第一级：精确版本匹配
        if (StringUtils.hasText(version)) {
            List<ServiceInstance> matched = instances.stream()
                    .filter(i -> version.equals(i.getMetadata().get(properties.getHeader())))
                    .collect(Collectors.toList());
            if (!matched.isEmpty()) {
                log.debug("[{}] Routing to version {} instance", serviceId, version);
                return randomResponse(matched);
            }
        }

        // 第二级：无版本实例
        List<ServiceInstance> noVersionInstances = instances.stream()
                .filter(i -> !i.getMetadata().containsKey(properties.getHeader()))
                .collect(Collectors.toList());
        if (!noVersionInstances.isEmpty()) {
            log.debug("[{}] Routing to no-version instance", serviceId);
            return randomResponse(noVersionInstances);
        }

        // 第三级：强制兜底
        log.warn("[{}] Using fallback routing", serviceId);
        return randomResponse(instances);
    }

    private Response<ServiceInstance> randomResponse(List<ServiceInstance> instances) {
        // 获取自定义负载均衡策略
        QdtLoadBalanceFactory loadBalanceFactory = SpringUtil.getBean(properties.getBiz(), QdtLoadBalanceFactory.class);
        return new DefaultResponse(instances.get(loadBalanceFactory.getNodeIndex(serviceId, instances.size())));
    }
}

