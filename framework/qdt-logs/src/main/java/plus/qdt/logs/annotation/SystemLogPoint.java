package plus.qdt.logs.annotation;

import java.lang.annotation.*;

/**
 * 系统日志AOP注解
 *
 * <AUTHOR>
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
public @interface SystemLogPoint {

    /**
     * 日志名称
     *
     * @return 日志名称
     */
    String description() default "";

    /**
     * 自定义日志内容
     *
     * @return 日志内容
     */
    String customerLog() default "";
}
