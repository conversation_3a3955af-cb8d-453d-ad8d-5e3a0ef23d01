package plus.qdt.mybatis.mybatisplus;

import plus.qdt.common.security.AuthUser;
import plus.qdt.common.security.context.UserContext;
import plus.qdt.common.utils.SnowFlake;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 字段填充审计
 *
 * <AUTHOR>
 */
@Component
public class MyMetaObjectHandler implements MetaObjectHandler {

    public static final String UPDATE_BY = "updateBy";

    @Override
    public void insertFill(MetaObject metaObject) {
        AuthUser authUser = UserContext.getCurrentUser();
        if (authUser != null) {
            this.setFieldValByName("createBy", authUser.getId(), metaObject);
            this.setFieldValByName(UPDATE_BY, authUser.getId(), metaObject);
        } else {
            this.setFieldValByName("createBy", "SYSTEM", metaObject);
            this.setFieldValByName(UPDATE_BY, "SYSTEM", metaObject);
        }
        //有创建时间字段
        if (metaObject.hasGetter("createTime")) {
            this.setFieldValByName("createTime", new Date(), metaObject);
        }
        //有值，则写入
        if (metaObject.hasGetter("deleteFlag")) {
            this.setFieldValByName("deleteFlag", false, metaObject);
        }
        if (metaObject.hasGetter("id") && metaObject.getValue("id") == null) {
            //如果已经配置id，则不再写入
            this.setFieldValByName("id", String.valueOf(SnowFlake.getId()), metaObject);
        }
        this.setFieldValByName("updateTime", new Date(), metaObject);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        AuthUser authUser = UserContext.getCurrentUser();
        if (authUser != null) {
            this.setFieldValByName(UPDATE_BY, authUser.getId(), metaObject);
        }
        this.setFieldValByName("updateTime", new Date(), metaObject);
    }
}

