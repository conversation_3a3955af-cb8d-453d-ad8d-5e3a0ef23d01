package plus.qdt.common.utils;

import cn.hutool.core.map.MapUtil;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import plus.qdt.common.properties.DomainProperties;
import plus.qdt.common.vo.CardType;

import java.util.HashMap;
import java.util.Map;

/**
 * 企业机器人通知模板
 *
 * <AUTHOR>
 * @since 2.0
 */
public class BotTemplateUtils {

    private final static DomainProperties DOMAIN_PROPERTIES = SpringContextUtil.getBean(DomainProperties.class);

    /**
     * 发送店铺审核通知
     * @param template 店铺内容
     * <AUTHOR>
     */
    public static void sendStore(CardType template) {
        send(DOMAIN_PROPERTIES.getServiceStore(), template);
    }

    /**
     * 发送异常消息通知
     * @param url 通知地址
     * @param request 请求
     * @param applicationName 服务
     * @param message 异常内容
     * <AUTHOR>
     */
    public static void sendException(String url, HttpServletRequest request, String applicationName, String message) {
        send(url, "### ⚠ 接口异常通知\n\n**追踪ID**：" + TraceContext.traceId()
                + "\n**接口地址**：" + request.getRequestURI() +
                "\n**请求方法**：" + request.getMethod() +
                "\n**请求参数**：" + request.getQueryString() +
                "\n**服务名称**：" + applicationName +
                "\n**异常信息**：" + message);
    }


    /**
     * 向企业微信机器人发送消息
     * @param url 地址
     * @param template 发送模板内容
     * <AUTHOR>
     */
    public static void send(String url, String template) {
        Map<String, Object> map = new HashMap<>();
        map.put("msgtype", "markdown");
        Map<String, String> markdown = new HashMap<>();
        markdown.put("content", template);
        map.put("markdown", markdown);
        HttpUtils.doPostWithJson(url, map);
    }


    /**
     * 向企业微信机器人发送消息
     * @param url 地址
     * @param template 发送模板内容
     * <AUTHOR>
     */
    public static void send(String url, CardType template) {
        Map<String, Object> map = new HashMap<>();
        map.put("msgtype", "template_card");
        Map<String, Object> markdown = new HashMap<>();
        markdown.put("card_type", "news_notice");
        markdown.put("source", MapUtil.builder()
                .put("icon_url", "https://zkqdt.oss-cn-hangzhou.aliyuncs.com/MANAGER/1656220798258995200/undefined/3e941f24fddb41a5acd749da0b3a2369.png")
                .put("desc", "企道通+24")
                .put("desc_color", 0)
                .build());
        markdown.put("main_title", MapUtil.builder()
                .put("title", template.title())
                .put("desc", template.subTitle())
                .build());
        if (StringUtils.isNotBlank(template.image())) {
            markdown.put("card_image", MapUtil.builder()
                    .put("url", template.image())
                    .put("aspect_ratio", 2.25)
                    .build());
        }
        markdown.put("card_action", MapUtil.builder()
                .put("type", 2)
                .put("appid", "wx9d36ca348a4a42d2")
                .build());
        map.put("template_card", markdown);
        HttpUtils.doPostWithJson(url, map);
    }

}
