package plus.qdt.common.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 域名配置
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "lili.domain")
public class DomainProperties {

    /**
     * 买家PC端域名
     */
    private String pc;

    /**
     * 外网网关地址
     */
    private String callback;

    /**
     * 买家WAP端域名
     */
    private String wap;

    /**
     * Store域名
     */
    private String store;

    /**
     * 管理端域名
     */
    private String admin;

    /**
     * 机器人url
     */
    private String bot;

    /**
     * 触发主机
     */
    private String host;

    /**
     * 服务通知商家入驻
     */
    private String serviceStore;
}
