package plus.qdt.common.validation.impl;

import plus.qdt.common.validation.EnumValue;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

/**
 * 枚举之校验
 *
 * <AUTHOR>
 * @since 2021/7/9 1:41 上午
 */
public class EnumValuesValidator implements ConstraintValidator<EnumValue, Object> {

    private String[] strValues;
    private int[] intValues;

    @Override
    public boolean isValid(Object o, ConstraintValidatorContext constraintValidatorContext) {
        if (o instanceof String) {
            for (String s : strValues) {
                if (s.equals(o)) {
                    return true;
                }
            }
        } else if (o instanceof Integer integerValue) {
            for (int s : intValues) {
                if (s == integerValue) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public void initialize(EnumValue constraintAnnotation) {
        strValues = constraintAnnotation.strValues();
        intValues = constraintAnnotation.intValues();
    }
}
