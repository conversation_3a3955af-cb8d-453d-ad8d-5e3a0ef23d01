package plus.qdt.common.tpi.yzh.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 云中鹤TPI配置属性
 * 
 * <AUTHOR>
 * @since 2025-06-12
 */
@Data
@Component
@ConfigurationProperties(prefix = "yzh.tpi")
public class YzhTpiProperties {
    /**
     * 获取当前环境的基础URL
     *
     * @return 基础URL
     */
    public String getBaseUrl() {
        return "prod".equalsIgnoreCase(env) ? prodUrl : testUrl;
    }

    /**
     * 测试环境地址
     */
    private String testUrl = "https://openapi2-show.haoxiny.com";

    /**
     * 生产环境地址
     */
    private String prodUrl = "https://openapi2.haoxiny.com";

    /**
     * 当前使用的环境：test/prod
     */
    private String env = "test";

    /**
     * 应用Key
     */
    private String appKey;

    /**
     * 应用Secret（明文）
     */
    private String appSecret;

    /**
     * 签名加密类型：MD5/SM3
     */
    private String grantType;

    /**
     * Token获取接口路径
     */
    private String tokenPath = "/open/api/access/queryAccessToken";

    /**
     * 商品列表获取接口路径
     */
    private String goodsListPath = "/open/api/goods/queryGoodsList";


    /**
     * 商品详情获取接口路径
     */
    private String goodsDetailPath = "/open/api/goods/queryGoodsDetail";

    /**
     * 创建订单接口路径
     */
    private String createOrderPath = "/open/api/order/createOrder";

    /**
     * 区县地址接口路径
     */
    private String allAddressPath = "/open/api/address/queryAreaList";

    /**
     * 省份地址接口路径
     */
    private String provinceAddressPath = "/open/api/address/queryAddressProvince";

    /**
     * 城市地址接口路径
     */
    private String cityAddressPath = "/open/api/address/queryAddressCity";

    /**
     * 区县地址接口路径
     */
    private String areaAddressPath = "/open/api/address/queryAddressArea";

    /**
     * 街道地址接口路径
     */
    private String streetAddressPath = "/open/api/address/queryAddressStreet";

    /**
     * 取消订单接口路径
     */
    private String cancelOrderPath = "/open/api/order/cancelOrder";

    /**
     * 查询订单物流接口路径
     */
    private String queryOrderLogisticsPath = "/open/api/order/queryOrderLogistics";
    
    /**
     * 云中鹤查询订单可以申请的售后类型
         * @param null 
     * @return void
     * <AUTHOR>
     */
    private String queryOrderAvailabeReturnTypePath = "/open/api/afterSalesOrder/queryOrderAvailabeReturnType";

    /**
     * 查询订单的商品可以申请售后商品数量接口
     * @param null
     * @return void
     * <AUTHOR>
     */
    private String queryOrderAvailabeGoodsQtyPath = "/open/api/afterSalesOrder/queryOrderAvailabeGoodsQty";

    /**
     * 提交售后订单接口
         * @param null 
     * @return void
     * <AUTHOR>
     */
    private String createAfterSaleOrderPath = "/open/api/afterSalesOrder/createAfterSalesOrder";

    /**
     * 取消售后订单接口
         * @param null
     * @return void
     * <AUTHOR>
     */
    private String cancelAfterSalesPath = "/open/api/afterSalesOrder/cancelAfterSalesOrder";

    /**
     * 售后商品寄回
         * @param null
     * @return void
     * <AUTHOR>
     */
    private String sendBackGoodsPath = "/open/api/afterSalesOrder/sendBackGoods";

    public String getSendBackGoodsUrl(){
        return getBaseUrl() + sendBackGoodsPath;
    }

    private String deleteMsgPath = "/open/api/message/delete";

    public String getDeleteMsgUrl(){
        return getBaseUrl() + deleteMsgPath;
    }

    /**
     * 消息列表接口
         * @param null
     * @return void
     * <AUTHOR>
     */

    private String msgListPath = "/open/api/message/queryMessageListByCondition";

    public String getMsgListUrl(){
        return getBaseUrl() + msgListPath;
    }

    /**
     * 获取Token获取的完整URL
     *
     * @return Token获取URL
     */
    public String getTokenUrl() {
        return getBaseUrl() + tokenPath;
    }

    /**
     * 获取商品基础数据接口URL
     *
     * @return 商品列表接口URL
     */
    public String getGoodsListUrl() {
        return getBaseUrl() + goodsListPath;
    }

    /**
     * 获取商品详情信息接口URL
     *
     * @return 商品详情接口URL
     */
    public String getGoodsDetailUrl() {
        return getBaseUrl() + goodsDetailPath;
    }

    /**
     * 获取创建订单接口URL
     *
     * @return 创建订单接口URL
     * <AUTHOR>
     */
    public String getCreateOrderUrl(){
        return getBaseUrl() + createOrderPath;
    }

    /**
     * 获取地址查询接口URL
     *
     * @return 地址查询接口URL
     * <AUTHOR>
     */
    public String getAddressUrl(){
        return getBaseUrl() + allAddressPath;
    }

    /**
     * 获取省份地址接口URL
     * @return 省份地址接口URL
     */
    public String getProvinceAddressUrl(){
        return getBaseUrl() + provinceAddressPath;
    }

    /**
     * 获取城市地址接口URL
     * @return 城市地址接口URL
     */
    public String getCityAddressUrl(){
        return getBaseUrl() + cityAddressPath;
    }

    /**
     * 获取区县地址接口URL
     * @return 区县地址接口URL
     */
    public String getAreaAddressUrl(){
        return getBaseUrl() + areaAddressPath;
    }

    /**
     * 获取街道地址接口URL
     * @return 街道地址接口URL
     */
    public String getStreetAddressUrl(){
        return getBaseUrl() + streetAddressPath;
    }

    /**
     * 获取取消订单接口URL
     *
     * @return 取消订单接口URL
     * <AUTHOR>
     */
    public String getCancelOrderUrl(){
        return getBaseUrl() + cancelOrderPath;
    }

    /**
     * 获取查询物流轨迹接口URL
     *
     * @return 查询物流轨迹接口URL
     * <AUTHOR>
     */
    public String getQueryOrderLogisticsUrl(){
        return getBaseUrl() + queryOrderLogisticsPath;
    }

    /**
     * 获取查询订单可以申请的售后类型接口URL
     *
     * @return 查询订单可以申请的售后类型接口URL
     * <AUTHOR>
     */
    public String getQueryOrderAvailabeReturnTypeUrl(){
        return getBaseUrl() + queryOrderAvailabeReturnTypePath;
    }

    /**
     * 获取查询订单的商品可以申请售后商品数量接口URL
     *
     * @return 查询订单的商品可以申请售后商品数量接口URL
     * <AUTHOR>
     */
    public String getQueryOrderAvailabeGoodsQtyUrl(){
        return getBaseUrl() + queryOrderAvailabeGoodsQtyPath;
    }

    /**
     * 提交售后订单接口
     * @return void
     * <AUTHOR>
     */
    public String getCreateAfterSaleOrderUrl(){
        return getBaseUrl() + createAfterSaleOrderPath;
    }

    /**
     * 取消售后订单接口
     * @return void
     * <AUTHOR>
     */
    public String getCancelAfterSalesUrl(){
        return getBaseUrl() + cancelAfterSalesPath;
    }
}
