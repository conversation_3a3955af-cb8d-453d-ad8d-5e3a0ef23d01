package plus.qdt.common.vo;

import cn.hutool.core.text.CharSequenceUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import plus.qdt.common.utils.StringUtils;

/**
 * 查询参数
 *
 * <AUTHOR>
 */
@Data
public class PageVO {
    
    @Schema(title = "当前页")
    private Integer pageNumber = 1;

    @Schema(title = "每页显示数量")
    private Integer pageSize = 10;

    @Schema(title = "排序字段")
    private String sort;

    @Schema(title = "排序方式 asc/desc")
    private String order;

    @Schema(title = "需要驼峰转换蛇形", description = "一般不做处理，如果数据库中就是蛇形，则这块需要处理。")
    private Boolean notConvert;

    public String getSort() {
        if (!CharSequenceUtil.isEmpty(sort)) {
            if (notConvert == null || Boolean.FALSE.equals(notConvert)) {
                return StringUtils.camel2Underline(sort);
            } else {
                return sort;
            }
        }
        return sort;
    }

    public Integer getPageSize() {
        return pageSize > 500 ? 500 : pageSize;
    }
}
