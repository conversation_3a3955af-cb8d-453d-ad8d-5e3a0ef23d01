package plus.qdt.common.tpi.yzh.utils;

import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 云中鹤TPI签名工具类
 * 
 * <AUTHOR>
 * @since 2025-06-12
 */
@Slf4j
public class SignUtils {

    /**
     * MD5加密
     * 
     * @param data 待加密数据
     * @return 加密后的字符串（小写）
     */
    public static String md5(String data) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(data.getBytes(StandardCharsets.UTF_8));
            byte[] digest = md.digest();
            
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString().toLowerCase();
        } catch (Exception e) {
            log.error("MD5加密失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * SM3加密（使用简单实现，如果项目中有hutool可以使用hutool的SM3）
     * 
     * @param data 待加密数据
     * @return 加密后的字符串
     */
    public static String sm3(String data) {
        try {
            // 这里使用MD5作为SM3的替代实现
            // 如果项目中有hutool依赖，可以使用：
            // SM3 sm3 = new SM3();
            // return sm3.digestHex(data);
            
            // 临时使用MD5实现，建议项目中添加hutool依赖后使用真正的SM3
            log.warn("当前使用MD5替代SM3实现，建议添加hutool依赖使用真正的SM3加密");
            return md5(data);
        } catch (Exception e) {
            log.error("SM3加密失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 生成签名
     * 签名规则：appKey+appSecret+timestamp+appSecret
     * 
     * @param appKey 应用Key
     * @param appSecret 应用Secret（明文）
     * @param timestamp 时间戳
     * @param grantType 加密类型（MD5/SM3）
     * @return 签名字符串
     */
    public static String generateSign(String appKey, String appSecret, String timestamp, String grantType) {
        // 拼接签名字符串：appKey+appSecret+timestamp+appSecret
        String signStr = appKey + appSecret + timestamp + appSecret;

        log.debug("签名原始字符串: {}", signStr);
        
        // 根据加密类型进行加密
        String sign;
        if ("SM3".equalsIgnoreCase(grantType)) {
            sign = sm3(signStr);
        } else {
            // 默认使用MD5
            sign = md5(signStr);
        }
        
        log.debug("生成的签名: {}", sign);
        return sign;
    }

    /**
     * 加密appSecret
     * 根据文档说明，appSecret需要使用MD5或SM3加密后传输
     * 
     * @param appSecret 明文appSecret
     * @param grantType 加密类型（MD5/SM3）
     * @return 加密后的appSecret
     */
    public static String encryptAppSecret(String appSecret, String grantType) {
        if ("SM3".equalsIgnoreCase(grantType)) {
            return sm3(appSecret);
        } else {
            // 默认使用MD5
            return md5(appSecret);
        }
    }

    /**
     * 获取当前时间字符串（格式：yyyy-MM-dd HH:mm:ss）
     *
     * @return 格式化的时间字符串
     */
    public static String getCurrentTimestamp() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return LocalDateTime.now().format(formatter);
    }
}
